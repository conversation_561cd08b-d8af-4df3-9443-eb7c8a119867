2025-07-16 21:11:42,821 - Poster - INFO - شروع فرآیند ورود به X...
2025-07-16 21:11:49,018 - Poster - ERROR - خطا در ارسال نام کاربری: 401
2025-07-16 21:12:55,597 - Poster - INFO - شروع فرآیند ورود به X...
2025-07-16 21:12:58,574 - Poster - ERROR - خطا در ارسال نام کاربری: 401
2025-07-16 21:13:39,149 - Poster - INFO - شروع فرآیند ورود به X...
2025-07-16 21:13:45,613 - Poster - ERROR - خطا در ارسال نام کاربری: 401
2025-07-16 21:14:26,553 - Poster - INFO - شروع فرآیند ورود به X...
2025-07-16 21:14:30,034 - Poster - ERROR - خطا در ارسال نام کاربری: 401
2025-07-16 21:17:13,384 - Poster - INFO - شروع فرآیند ورود به X...
2025-07-16 21:17:13,389 - Poster - INFO - شبیه‌سازی ورود...
2025-07-16 21:17:15,390 - Poster - INFO - ورود به X موفقیت‌آمیز بود (شبیه‌سازی)
2025-07-16 21:23:51,835 - Poster - INFO - شروع فرآیند ورود به X...
2025-07-16 21:23:51,836 - Poster - INFO - شبیه‌سازی ورود...
2025-07-16 21:23:53,844 - Poster - INFO - ورود به X موفقیت‌آمیز بود (شبیه‌سازی)
2025-07-16 21:24:24,706 - Poster - INFO - شروع فرآیند ورود به X...
2025-07-16 21:24:24,706 - Poster - INFO - شبیه‌سازی ورود...
2025-07-16 21:24:26,707 - Poster - INFO - ورود به X موفقیت‌آمیز بود (شبیه‌سازی)
2025-07-16 23:42:43,903 - Poster - INFO - شروع فرآیند ورود به X...
2025-07-16 23:42:43,903 - Poster - INFO - شبیه‌سازی ورود...
2025-07-16 23:42:45,904 - Poster - INFO - ورود به X موفقیت‌آمیز بود (شبیه‌سازی)
2025-07-16 23:44:34,659 - Poster - INFO - شروع فرآیند ورود به X...
2025-07-16 23:44:34,659 - Poster - INFO - شبیه‌سازی ورود...
2025-07-16 23:44:36,660 - Poster - INFO - ورود به X موفقیت‌آمیز بود (شبیه‌سازی)
2025-07-16 23:49:43,667 - Poster - INFO - شروع فرآیند ورود به X...
2025-07-16 23:49:43,668 - Poster - INFO - شبیه‌سازی ورود...
2025-07-16 23:49:45,670 - Poster - INFO - ورود به X موفقیت‌آمیز بود (شبیه‌سازی)
2025-07-17 17:45:55,006 - Poster - WARNING - تلاش اول ناموفق: Message: session not created: cannot connect to chrome at 127.0.0.1:56203
from chrome not reachable; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#sessionnotcreatedexception
Stacktrace:
	GetHandleVerifier [0x0x1171a33+62339]
	GetHandleVerifier [0x0x1171a74+62404]
	(No symbol) [0x0xfb1f80]
	(No symbol) [0x0xfa5f2a]
	(No symbol) [0x0xfeadc6]
	(No symbol) [0x0xfe128f]
	(No symbol) [0x0xfe10c6]
	(No symbol) [0x0x102ae77]
	(No symbol) [0x0x102a76a]
	(No symbol) [0x0x101f1b6]
	(No symbol) [0x0xfee7a2]
	(No symbol) [0x0xfef644]
	GetHandleVerifier [0x0x13e65c3+2637587]
	GetHandleVerifier [0x0x13e19ca+2618138]
	GetHandleVerifier [0x0x11984aa+220666]
	GetHandleVerifier [0x0x11888d8+156200]
	GetHandleVerifier [0x0x118f06d+182717]
	GetHandleVerifier [0x0x1179978+94920]
	GetHandleVerifier [0x0x1179b02+95314]
	GetHandleVerifier [0x0x1164c4a+9626]
	BaseThreadInitThunk [0x0x76f35d49+25]
	RtlInitializeExceptionChain [0x0x7747d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7747d131+561]

2025-07-17 17:47:19,134 - Poster - ERROR - ❌ خطا در راه‌اندازی driver: Message: session not created: cannot connect to chrome at 127.0.0.1:56331
from chrome not reachable; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#sessionnotcreatedexception
Stacktrace:
	GetHandleVerifier [0x0x571a33+62339]
	GetHandleVerifier [0x0x571a74+62404]
	(No symbol) [0x0x3b1f80]
	(No symbol) [0x0x3a5f2a]
	(No symbol) [0x0x3eadc6]
	(No symbol) [0x0x3e128f]
	(No symbol) [0x0x3e10c6]
	(No symbol) [0x0x42ae77]
	(No symbol) [0x0x42a76a]
	(No symbol) [0x0x41f1b6]
	(No symbol) [0x0x3ee7a2]
	(No symbol) [0x0x3ef644]
	GetHandleVerifier [0x0x7e65c3+2637587]
	GetHandleVerifier [0x0x7e19ca+2618138]
	GetHandleVerifier [0x0x5984aa+220666]
	GetHandleVerifier [0x0x5888d8+156200]
	GetHandleVerifier [0x0x58f06d+182717]
	GetHandleVerifier [0x0x579978+94920]
	GetHandleVerifier [0x0x579b02+95314]
	GetHandleVerifier [0x0x564c4a+9626]
	BaseThreadInitThunk [0x0x76f35d49+25]
	RtlInitializeExceptionChain [0x0x7747d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7747d131+561]

2025-07-17 17:49:32,884 - Poster - WARNING - تلاش اول ناموفق: Message: session not created: cannot connect to chrome at 127.0.0.1:56492
from chrome not reachable; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#sessionnotcreatedexception
Stacktrace:
	GetHandleVerifier [0x0x8b1a33+62339]
	GetHandleVerifier [0x0x8b1a74+62404]
	(No symbol) [0x0x6f1f80]
	(No symbol) [0x0x6e5f2a]
	(No symbol) [0x0x72adc6]
	(No symbol) [0x0x72128f]
	(No symbol) [0x0x7210c6]
	(No symbol) [0x0x76ae77]
	(No symbol) [0x0x76a76a]
	(No symbol) [0x0x75f1b6]
	(No symbol) [0x0x72e7a2]
	(No symbol) [0x0x72f644]
	GetHandleVerifier [0x0xb265c3+2637587]
	GetHandleVerifier [0x0xb219ca+2618138]
	GetHandleVerifier [0x0x8d84aa+220666]
	GetHandleVerifier [0x0x8c88d8+156200]
	GetHandleVerifier [0x0x8cf06d+182717]
	GetHandleVerifier [0x0x8b9978+94920]
	GetHandleVerifier [0x0x8b9b02+95314]
	GetHandleVerifier [0x0x8a4c4a+9626]
	BaseThreadInitThunk [0x0x76f35d49+25]
	RtlInitializeExceptionChain [0x0x7747d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7747d131+561]

2025-07-17 17:51:48,714 - Poster - ERROR - ❌ خطا در راه‌اندازی driver: Message: session not created: cannot connect to chrome at 127.0.0.1:56709
from chrome not reachable; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#sessionnotcreatedexception
Stacktrace:
	GetHandleVerifier [0x0x781a33+62339]
	GetHandleVerifier [0x0x781a74+62404]
	(No symbol) [0x0x5c1f80]
	(No symbol) [0x0x5b5f2a]
	(No symbol) [0x0x5fadc6]
	(No symbol) [0x0x5f128f]
	(No symbol) [0x0x5f10c6]
	(No symbol) [0x0x63ae77]
	(No symbol) [0x0x63a76a]
	(No symbol) [0x0x62f1b6]
	(No symbol) [0x0x5fe7a2]
	(No symbol) [0x0x5ff644]
	GetHandleVerifier [0x0x9f65c3+2637587]
	GetHandleVerifier [0x0x9f19ca+2618138]
	GetHandleVerifier [0x0x7a84aa+220666]
	GetHandleVerifier [0x0x7988d8+156200]
	GetHandleVerifier [0x0x79f06d+182717]
	GetHandleVerifier [0x0x789978+94920]
	GetHandleVerifier [0x0x789b02+95314]
	GetHandleVerifier [0x0x774c4a+9626]
	BaseThreadInitThunk [0x0x76f35d49+25]
	RtlInitializeExceptionChain [0x0x7747d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7747d131+561]

