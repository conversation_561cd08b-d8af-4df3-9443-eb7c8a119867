"""
رابط گرافیکی اصلی ربات
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
from datetime import datetime
from typing import Dict, List
import time

from crypto_news_bot.config import WINDOW_TITLE, WINDOW_SIZE
from crypto_news_bot.utils.logger import gui_logger
from crypto_news_bot.database.models import DatabaseManager
from crypto_news_bot.gui.settings_window import SettingsWindow
from crypto_news_bot.gui.stats_window import StatsWindow
from crypto_news_bot.gui.live_chart import RealTimeStatsWidget
from crypto_news_bot.core.bot_manager import BotManager


class CryptoBotGUI:
    """کلاس رابط گرافیکی اصلی"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.db = DatabaseManager()
        self.bot_manager = BotManager()
        self.is_running = False
        
        self.setup_ui()
        self.setup_menu()
        self.update_status()
        
    def setup_ui(self):
        """راه‌اندازی رابط کاربری"""
        self.root.title(WINDOW_TITLE)
        self.root.geometry(f"{WINDOW_SIZE[0]}x{WINDOW_SIZE[1]}")
        self.root.resizable(True, True)
        
        # تنظیم آیکون (در صورت وجود)
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
        
        # ایجاد notebook برای تب‌ها
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # تب کنترل
        self.create_control_tab()
        
        # تب لاگ‌ها
        self.create_logs_tab()
        
        # تب آمار
        self.create_stats_tab()
        
        # تب منابع خبری
        self.create_sources_tab()

        # تب فعالیت زنده
        self.create_live_activity_tab()

        # تب وضعیت سیستم
        self.create_system_status_tab()

        # نوار وضعیت
        self.create_status_bar()
    
    def setup_menu(self):
        """ایجاد منوی اصلی"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # منوی فایل
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="فایل", menu=file_menu)
        file_menu.add_command(label="تنظیمات", command=self.open_settings)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.on_closing)
        
        # منوی ابزارها
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ابزارها", menu=tools_menu)
        tools_menu.add_command(label="آمار تفصیلی", command=self.open_stats)
        tools_menu.add_command(label="پاک کردن لاگ‌ها", command=self.clear_logs)
        tools_menu.add_command(label="تست اتصال", command=self.test_connection)
        
        # منوی راهنما
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="راهنما", menu=help_menu)
        help_menu.add_command(label="درباره", command=self.show_about)
    
    def create_control_tab(self):
        """ایجاد تب کنترل"""
        control_frame = ttk.Frame(self.notebook)
        self.notebook.add(control_frame, text="کنترل ربات")
        
        # قسمت وضعیت
        status_group = ttk.LabelFrame(control_frame, text="وضعیت ربات", padding=10)
        status_group.pack(fill=tk.X, padx=10, pady=5)
        
        self.status_label = ttk.Label(status_group, text="متوقف", font=("Arial", 12, "bold"))
        self.status_label.pack(side=tk.LEFT)
        
        self.status_indicator = tk.Canvas(status_group, width=20, height=20)
        self.status_indicator.pack(side=tk.RIGHT)
        self.status_circle = self.status_indicator.create_oval(2, 2, 18, 18, fill="red")
        
        # دکمه‌های کنترل
        control_buttons = ttk.Frame(control_frame)
        control_buttons.pack(fill=tk.X, padx=10, pady=10)
        
        self.start_button = ttk.Button(control_buttons, text="شروع ربات", 
                                      command=self.start_bot, style="Accent.TButton")
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(control_buttons, text="توقف ربات", 
                                     command=self.stop_bot, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        self.pause_button = ttk.Button(control_buttons, text="مکث", 
                                      command=self.pause_bot, state=tk.DISABLED)
        self.pause_button.pack(side=tk.LEFT, padx=5)
        
        # آمار سریع
        quick_stats = ttk.LabelFrame(control_frame, text="آمار زنده", padding=10)
        quick_stats.pack(fill=tk.X, padx=10, pady=5)

        stats_frame = ttk.Frame(quick_stats)
        stats_frame.pack(fill=tk.X)

        # ستون اول
        col1 = ttk.Frame(stats_frame)
        col1.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        ttk.Label(col1, text="📰 خبرهای امروز:").pack(anchor=tk.W)
        self.news_today_label = ttk.Label(col1, text="0", font=("Arial", 16, "bold"), foreground="blue")
        self.news_today_label.pack(anchor=tk.W)

        ttk.Label(col1, text="📤 پست‌های امروز:").pack(anchor=tk.W, pady=(10, 0))
        self.posts_today_label = ttk.Label(col1, text="0", font=("Arial", 16, "bold"), foreground="green")
        self.posts_today_label.pack(anchor=tk.W)

        # ستون دوم
        col2 = ttk.Frame(stats_frame)
        col2.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        ttk.Label(col2, text="₿ خبرهای کریپتو:").pack(anchor=tk.W)
        self.crypto_news_label = ttk.Label(col2, text="0", font=("Arial", 16, "bold"), foreground="orange")
        self.crypto_news_label.pack(anchor=tk.W)

        ttk.Label(col2, text="🔄 آخرین بررسی:").pack(anchor=tk.W, pady=(10, 0))
        self.last_check_label = ttk.Label(col2, text="هرگز", font=("Arial", 10))
        self.last_check_label.pack(anchor=tk.W)

        # ستون سوم - وضعیت فعلی
        col3 = ttk.Frame(stats_frame)
        col3.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        ttk.Label(col3, text="⚡ وضعیت فعلی:").pack(anchor=tk.W)
        self.current_activity_label = ttk.Label(col3, text="آماده", font=("Arial", 12, "bold"), foreground="gray")
        self.current_activity_label.pack(anchor=tk.W)

        ttk.Label(col3, text="📊 نرخ موفقیت:").pack(anchor=tk.W, pady=(10, 0))
        self.success_rate_label = ttk.Label(col3, text="0%", font=("Arial", 12, "bold"), foreground="purple")
        self.success_rate_label.pack(anchor=tk.W)
        
        # تنظیمات سریع
        quick_settings = ttk.LabelFrame(control_frame, text="تنظیمات سریع", padding=10)
        quick_settings.pack(fill=tk.X, padx=10, pady=5)
        
        # فاصله زمانی بررسی
        interval_frame = ttk.Frame(quick_settings)
        interval_frame.pack(fill=tk.X, pady=2)

        ttk.Label(interval_frame, text="فاصله بررسی (دقیقه):").pack(side=tk.LEFT)
        self.interval_var = tk.StringVar(value="5")
        interval_spinbox = ttk.Spinbox(interval_frame, from_=1, to=60, width=10,
                                      textvariable=self.interval_var,
                                      command=self.save_quick_settings)
        interval_spinbox.pack(side=tk.RIGHT)
        interval_spinbox.bind('<Return>', lambda e: self.save_quick_settings())

        # حداکثر پست روزانه
        max_posts_frame = ttk.Frame(quick_settings)
        max_posts_frame.pack(fill=tk.X, pady=2)

        ttk.Label(max_posts_frame, text="حداکثر پست روزانه:").pack(side=tk.LEFT)
        self.max_posts_var = tk.StringVar(value="50")
        max_posts_spinbox = ttk.Spinbox(max_posts_frame, from_=1, to=100, width=10,
                                       textvariable=self.max_posts_var,
                                       command=self.save_quick_settings)
        max_posts_spinbox.pack(side=tk.RIGHT)
        max_posts_spinbox.bind('<Return>', lambda e: self.save_quick_settings())

        # دکمه ذخیره سریع
        ttk.Button(quick_settings, text="💾 ذخیره تنظیمات",
                  command=self.save_quick_settings).pack(pady=5)
    
    def create_logs_tab(self):
        """ایجاد تب لاگ‌ها"""
        logs_frame = ttk.Frame(self.notebook)
        self.notebook.add(logs_frame, text="لاگ‌ها")
        
        # فیلتر لاگ‌ها
        filter_frame = ttk.Frame(logs_frame)
        filter_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(filter_frame, text="سطح لاگ:").pack(side=tk.LEFT)
        self.log_level_var = tk.StringVar(value="همه")
        log_level_combo = ttk.Combobox(filter_frame, textvariable=self.log_level_var,
                                      values=["همه", "INFO", "WARNING", "ERROR"], width=10)
        log_level_combo.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(filter_frame, text="پاک کردن", command=self.clear_logs).pack(side=tk.RIGHT)
        ttk.Button(filter_frame, text="ذخیره", command=self.save_logs).pack(side=tk.RIGHT, padx=5)
        
        # منطقه نمایش لاگ‌ها
        self.log_text = scrolledtext.ScrolledText(logs_frame, height=20, wrap=tk.WORD)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # تنظیم رنگ‌ها برای انواع لاگ
        self.log_text.tag_config("INFO", foreground="blue")
        self.log_text.tag_config("WARNING", foreground="orange")
        self.log_text.tag_config("ERROR", foreground="red")
        self.log_text.tag_config("SUCCESS", foreground="green")
    
    def create_stats_tab(self):
        """ایجاد تب آمار"""
        stats_frame = ttk.Frame(self.notebook)
        self.notebook.add(stats_frame, text="آمار زنده")

        # ایجاد ویجت آمار زمان واقعی
        self.realtime_stats = RealTimeStatsWidget(stats_frame)
    
    def create_sources_tab(self):
        """ایجاد تب منابع خبری"""
        sources_frame = ttk.Frame(self.notebook)
        self.notebook.add(sources_frame, text="منابع خبری")
        
        # لیست منابع
        sources_list_frame = ttk.LabelFrame(sources_frame, text="منابع فعال", padding=10)
        sources_list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Treeview برای منابع
        sources_columns = ("نام", "URL", "وضعیت", "آخرین بررسی")
        self.sources_tree = ttk.Treeview(sources_list_frame, columns=sources_columns, 
                                        show="headings", height=8)
        
        for col in sources_columns:
            self.sources_tree.heading(col, text=col)
            self.sources_tree.column(col, width=200)
        
        sources_scrollbar = ttk.Scrollbar(sources_list_frame, orient=tk.VERTICAL, 
                                         command=self.sources_tree.yview)
        self.sources_tree.configure(yscrollcommand=sources_scrollbar.set)
        
        self.sources_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        sources_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # دکمه‌های مدیریت منابع
        sources_buttons = ttk.Frame(sources_frame)
        sources_buttons.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(sources_buttons, text="➕ اضافه کردن", command=self.add_source).pack(side=tk.LEFT)
        ttk.Button(sources_buttons, text="✏️ ویرایش", command=self.edit_source).pack(side=tk.LEFT, padx=5)
        ttk.Button(sources_buttons, text="🗑️ حذف", command=self.delete_source).pack(side=tk.LEFT)
        ttk.Button(sources_buttons, text="🔍 تست", command=self.test_source).pack(side=tk.LEFT, padx=5)
        ttk.Button(sources_buttons, text="🔄 به‌روزرسانی", command=self.update_sources_list).pack(side=tk.LEFT, padx=5)

        # نمایش تعداد منابع
        self.sources_count_label = ttk.Label(sources_buttons, text="تعداد منابع: 0", font=("Arial", 10))
        self.sources_count_label.pack(side=tk.RIGHT)

    def create_live_activity_tab(self):
        """ایجاد تب فعالیت زنده"""
        activity_frame = ttk.Frame(self.notebook)
        self.notebook.add(activity_frame, text="فعالیت زنده")

        # قسمت بالا - وضعیت فعلی
        current_status = ttk.LabelFrame(activity_frame, text="وضعیت فعلی", padding=10)
        current_status.pack(fill=tk.X, padx=10, pady=5)

        status_grid = ttk.Frame(current_status)
        status_grid.pack(fill=tk.X)

        # ردیف اول
        ttk.Label(status_grid, text="🔄 در حال انجام:", font=("Arial", 12, "bold")).grid(row=0, column=0, sticky=tk.W, padx=5)
        self.current_task_label = ttk.Label(status_grid, text="منتظر شروع...", font=("Arial", 12), foreground="blue")
        self.current_task_label.grid(row=0, column=1, sticky=tk.W, padx=10)

        # ردیف دوم
        ttk.Label(status_grid, text="⏱️ زمان باقی‌مانده:", font=("Arial", 12, "bold")).grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.time_remaining_label = ttk.Label(status_grid, text="--:--", font=("Arial", 12), foreground="red")
        self.time_remaining_label.grid(row=1, column=1, sticky=tk.W, padx=10, pady=5)

        # ردیف سوم
        ttk.Label(status_grid, text="📈 پیشرفت:", font=("Arial", 12, "bold")).grid(row=2, column=0, sticky=tk.W, padx=5)
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(status_grid, variable=self.progress_var, maximum=100, length=200)
        self.progress_bar.grid(row=2, column=1, sticky=tk.W, padx=10)

        self.progress_label = ttk.Label(status_grid, text="0%", font=("Arial", 10))
        self.progress_label.grid(row=2, column=2, sticky=tk.W, padx=5)

        # قسمت وسط - آخرین فعالیت‌ها
        recent_activity = ttk.LabelFrame(activity_frame, text="آخرین فعالیت‌ها", padding=10)
        recent_activity.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # لیست فعالیت‌ها
        activity_list_frame = ttk.Frame(recent_activity)
        activity_list_frame.pack(fill=tk.BOTH, expand=True)

        # Treeview برای نمایش فعالیت‌ها
        activity_columns = ("زمان", "نوع", "توضیحات", "وضعیت")
        self.activity_tree = ttk.Treeview(activity_list_frame, columns=activity_columns,
                                         show="headings", height=12)

        for col in activity_columns:
            self.activity_tree.heading(col, text=col)
            if col == "زمان":
                self.activity_tree.column(col, width=80)
            elif col == "نوع":
                self.activity_tree.column(col, width=100)
            elif col == "وضعیت":
                self.activity_tree.column(col, width=80)
            else:
                self.activity_tree.column(col, width=300)

        # اسکرول بار
        activity_scrollbar = ttk.Scrollbar(activity_list_frame, orient=tk.VERTICAL,
                                          command=self.activity_tree.yview)
        self.activity_tree.configure(yscrollcommand=activity_scrollbar.set)

        self.activity_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        activity_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # دکمه‌های کنترل فعالیت
        activity_buttons = ttk.Frame(activity_frame)
        activity_buttons.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(activity_buttons, text="پاک کردن لیست", command=self.clear_activity_log).pack(side=tk.LEFT)
        ttk.Button(activity_buttons, text="ذخیره گزارش", command=self.save_activity_report).pack(side=tk.LEFT, padx=5)

        # شروع به‌روزرسانی فعالیت
        self.update_live_activity()

    def create_system_status_tab(self):
        """ایجاد تب وضعیت سیستم"""
        system_frame = ttk.Frame(self.notebook)
        self.notebook.add(system_frame, text="وضعیت سیستم")

        # قسمت بالا - وضعیت کلی سیستم
        system_overview = ttk.LabelFrame(system_frame, text="وضعیت کلی سیستم", padding=10)
        system_overview.pack(fill=tk.X, padx=10, pady=5)

        overview_grid = ttk.Frame(system_overview)
        overview_grid.pack(fill=tk.X)

        # ردیف اول - وضعیت اجزا
        ttk.Label(overview_grid, text="🤖 ربات:", font=("Arial", 12, "bold")).grid(row=0, column=0, sticky=tk.W, padx=5)
        self.bot_status_label = ttk.Label(overview_grid, text="متوقف", font=("Arial", 12), foreground="red")
        self.bot_status_label.grid(row=0, column=1, sticky=tk.W, padx=10)

        ttk.Label(overview_grid, text="🗄️ پایگاه داده:", font=("Arial", 12, "bold")).grid(row=0, column=2, sticky=tk.W, padx=20)
        self.db_status_label = ttk.Label(overview_grid, text="متصل", font=("Arial", 12), foreground="green")
        self.db_status_label.grid(row=0, column=3, sticky=tk.W, padx=10)

        # ردیف دوم - اتصالات
        ttk.Label(overview_grid, text="🌐 اینترنت:", font=("Arial", 12, "bold")).grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.internet_status_label = ttk.Label(overview_grid, text="متصل", font=("Arial", 12), foreground="green")
        self.internet_status_label.grid(row=1, column=1, sticky=tk.W, padx=10, pady=5)

        ttk.Label(overview_grid, text="🔗 X (Twitter):", font=("Arial", 12, "bold")).grid(row=1, column=2, sticky=tk.W, padx=20, pady=5)
        self.x_status_label = ttk.Label(overview_grid, text="قطع", font=("Arial", 12), foreground="red")
        self.x_status_label.grid(row=1, column=3, sticky=tk.W, padx=10, pady=5)

        # قسمت وسط - آمار عملکرد
        performance_stats = ttk.LabelFrame(system_frame, text="آمار عملکرد", padding=10)
        performance_stats.pack(fill=tk.X, padx=10, pady=5)

        perf_grid = ttk.Frame(performance_stats)
        perf_grid.pack(fill=tk.X)

        # زمان اجرا
        ttk.Label(perf_grid, text="⏱️ زمان اجرا:", font=("Arial", 11, "bold")).grid(row=0, column=0, sticky=tk.W, padx=5)
        self.uptime_label = ttk.Label(perf_grid, text="00:00:00", font=("Arial", 11))
        self.uptime_label.grid(row=0, column=1, sticky=tk.W, padx=10)

        # استفاده از حافظه
        ttk.Label(perf_grid, text="💾 حافظه:", font=("Arial", 11, "bold")).grid(row=0, column=2, sticky=tk.W, padx=20)
        self.memory_label = ttk.Label(perf_grid, text="0 MB", font=("Arial", 11))
        self.memory_label.grid(row=0, column=3, sticky=tk.W, padx=10)

        # استفاده از CPU
        ttk.Label(perf_grid, text="⚡ CPU:", font=("Arial", 11, "bold")).grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.cpu_label = ttk.Label(perf_grid, text="0%", font=("Arial", 11))
        self.cpu_label.grid(row=1, column=1, sticky=tk.W, padx=10, pady=5)

        # تعداد thread ها
        ttk.Label(perf_grid, text="🧵 Thread ها:", font=("Arial", 11, "bold")).grid(row=1, column=2, sticky=tk.W, padx=20, pady=5)
        self.threads_label = ttk.Label(perf_grid, text="0", font=("Arial", 11))
        self.threads_label.grid(row=1, column=3, sticky=tk.W, padx=10, pady=5)

        # قسمت پایین - لاگ سیستم
        system_logs = ttk.LabelFrame(system_frame, text="لاگ سیستم", padding=10)
        system_logs.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # منطقه نمایش لاگ سیستم
        self.system_log_text = scrolledtext.ScrolledText(system_logs, height=8, wrap=tk.WORD)
        self.system_log_text.pack(fill=tk.BOTH, expand=True)

        # تنظیم رنگ‌ها برای انواع لاگ سیستم
        self.system_log_text.tag_config("INFO", foreground="blue")
        self.system_log_text.tag_config("WARNING", foreground="orange")
        self.system_log_text.tag_config("ERROR", foreground="red")
        self.system_log_text.tag_config("SUCCESS", foreground="green")

        # دکمه‌های کنترل سیستم
        system_buttons = ttk.Frame(system_frame)
        system_buttons.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(system_buttons, text="تست اتصال", command=self.test_connections).pack(side=tk.LEFT)
        ttk.Button(system_buttons, text="پاک کردن لاگ", command=self.clear_system_log).pack(side=tk.LEFT, padx=5)
        ttk.Button(system_buttons, text="بازنشانی آمار", command=self.reset_performance_stats).pack(side=tk.LEFT)

        # شروع مانیتورینگ سیستم
        self.start_system_monitoring()
    
    def create_status_bar(self):
        """ایجاد نوار وضعیت"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)
        
        self.status_text = ttk.Label(self.status_bar, text="آماده")
        self.status_text.pack(side=tk.LEFT, padx=5)
        
        # نمایش زمان
        self.time_label = ttk.Label(self.status_bar, text="")
        self.time_label.pack(side=tk.RIGHT, padx=5)
        
        self.update_time()
    
    def start_bot(self):
        """شروع ربات"""
        try:
            if not self.is_running:
                # بررسی تنظیمات ضروری
                if not self.check_required_settings():
                    return

                # بارگذاری مجدد تنظیمات
                self.bot_manager.reload_settings()

                # شروع ربات در thread جداگانه
                self.bot_thread = threading.Thread(target=self.bot_manager.start, daemon=True)
                self.bot_thread.start()

                self.is_running = True
                self.update_control_buttons()
                self.add_log("ربات شروع شد", "SUCCESS")
                self.add_activity_log("سیستم", "ربات شروع به کار کرد", "🟢")

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در شروع ربات: {e}")
            gui_logger.error(f"خطا در شروع ربات: {e}")
            self.add_activity_log("خطا", f"شروع ربات ناموفق: {e}", "❌")

    def check_required_settings(self):
        """بررسی تنظیمات ضروری"""
        # بررسی وجود منابع خبری
        sources = self.db.get_active_news_sources()
        if not sources:
            result = messagebox.askyesno(
                "منابع خبری",
                "هیچ منبع خبری فعالی یافت نشد.\nآیا می‌خواهید منابع پیش‌فرض اضافه شوند؟"
            )
            if result:
                self.add_default_sources()
            else:
                messagebox.showwarning("هشدار", "لطفاً ابتدا منابع خبری را اضافه کنید")
                return False

        # بررسی تنظیمات X
        username = self.db.get_setting("x_username", "")
        if not username:
            result = messagebox.askyesno(
                "تنظیمات X",
                "اطلاعات حساب X تنظیم نشده است.\nآیا می‌خواهید به تنظیمات بروید؟"
            )
            if result:
                self.open_settings()
            return False

        return True

    def add_default_sources(self):
        """اضافه کردن منابع پیش‌فرض"""
        default_sources = [
            ("CoinTelegraph", "https://twitter.com/cointelegraph"),
            ("CoinDesk", "https://twitter.com/coindesk"),
            ("Bitcoin Magazine", "https://twitter.com/bitcoinmagazine"),
            ("Crypto News", "https://twitter.com/cryptonews"),
            ("Decrypt", "https://twitter.com/decrypt_co")
        ]

        for name, url in default_sources:
            self.db.add_news_source(name, url)

        self.add_activity_log("سیستم", f"{len(default_sources)} منبع پیش‌فرض اضافه شد", "📰")
        self.update_sources_list()

    def save_quick_settings(self):
        """ذخیره تنظیمات سریع"""
        try:
            # ذخیره در دیتابیس
            self.db.set_setting("check_interval", self.interval_var.get())
            self.db.set_setting("max_posts_daily", self.max_posts_var.get())

            # بارگذاری مجدد تنظیمات در ربات
            if hasattr(self, 'bot_manager'):
                self.bot_manager.reload_settings()

            self.add_activity_log("تنظیمات", "تنظیمات سریع ذخیره شد", "💾")

        except Exception as e:
            gui_logger.error(f"خطا در ذخیره تنظیمات سریع: {e}")
            messagebox.showerror("خطا", f"خطا در ذخیره تنظیمات: {e}")

    def load_quick_settings(self):
        """بارگذاری تنظیمات سریع"""
        try:
            interval = self.db.get_setting("check_interval", "5")
            max_posts = self.db.get_setting("max_posts_daily", "50")

            self.interval_var.set(interval)
            self.max_posts_var.set(max_posts)

        except Exception as e:
            gui_logger.error(f"خطا در بارگذاری تنظیمات سریع: {e}")

    def stop_bot(self):
        """توقف ربات"""
        try:
            if self.is_running:
                self.bot_manager.stop()
                self.is_running = False
                self.update_control_buttons()
                self.add_log("ربات متوقف شد", "WARNING")
                self.add_activity_log("سیستم", "ربات متوقف شد", "🔴")

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در توقف ربات: {e}")
            gui_logger.error(f"خطا در توقف ربات: {e}")
            self.add_activity_log("خطا", f"توقف ربات ناموفق: {e}", "❌")

    def pause_bot(self):
        """مکث ربات"""
        try:
            self.bot_manager.pause()
            self.add_log("ربات در حالت مکث", "INFO")
            self.add_activity_log("سیستم", "ربات در حالت مکث", "⏸️")

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در مکث ربات: {e}")
            self.add_activity_log("خطا", f"مکث ربات ناموفق: {e}", "❌")
    
    def update_control_buttons(self):
        """به‌روزرسانی وضعیت دکمه‌ها"""
        if self.is_running:
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
            self.pause_button.config(state=tk.NORMAL)
            self.status_label.config(text="در حال اجرا")
            self.status_indicator.itemconfig(self.status_circle, fill="green")
        else:
            self.start_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)
            self.pause_button.config(state=tk.DISABLED)
            self.status_label.config(text="متوقف")
            self.status_indicator.itemconfig(self.status_circle, fill="red")

    def add_log(self, message: str, level: str = "INFO"):
        """اضافه کردن لاگ به نمایش"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}\n"

        self.log_text.insert(tk.END, log_entry, level)
        self.log_text.see(tk.END)

    def clear_logs(self):
        """پاک کردن لاگ‌ها"""
        self.log_text.delete(1.0, tk.END)

    def save_logs(self):
        """ذخیره لاگ‌ها"""
        from tkinter import filedialog
        filename = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(self.log_text.get(1.0, tk.END))

    def update_stats(self):
        """به‌روزرسانی آمار"""
        try:
            # پاک کردن آمار قبلی
            for item in self.stats_tree.get_children():
                self.stats_tree.delete(item)

            # دریافت آمار از دیتابیس
            # این قسمت باید پیاده‌سازی شود
            pass

        except Exception as e:
            gui_logger.error(f"خطا در به‌روزرسانی آمار: {e}")

    def export_stats(self):
        """صادرات آمار"""
        from tkinter import filedialog
        filename = filedialog.asksaveasfilename(
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )
        if filename:
            # پیاده‌سازی صادرات
            pass

    def add_source(self):
        """اضافه کردن منبع جدید"""
        dialog = tk.Toplevel(self.root)
        dialog.title("اضافه کردن منبع جدید")
        dialog.geometry("400x200")
        dialog.transient(self.root)
        dialog.grab_set()

        # نام منبع
        ttk.Label(dialog, text="نام منبع:").pack(pady=5)
        name_var = tk.StringVar()
        ttk.Entry(dialog, textvariable=name_var, width=40).pack(pady=5)

        # URL منبع
        ttk.Label(dialog, text="URL منبع (مثال: https://twitter.com/username):").pack(pady=5)
        url_var = tk.StringVar()
        ttk.Entry(dialog, textvariable=url_var, width=40).pack(pady=5)

        # دکمه‌ها
        buttons_frame = ttk.Frame(dialog)
        buttons_frame.pack(pady=20)

        def save_source():
            name = name_var.get().strip()
            url = url_var.get().strip()

            if not name or not url:
                messagebox.showerror("خطا", "لطفاً تمام فیلدها را پر کنید")
                return

            if self.db.add_news_source(name, url):
                self.add_activity_log("منابع", f"منبع جدید اضافه شد: {name}", "📰")
                self.update_sources_list()
                dialog.destroy()
            else:
                messagebox.showerror("خطا", "این منبع قبلاً وجود دارد")

        ttk.Button(buttons_frame, text="ذخیره", command=save_source).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="لغو", command=dialog.destroy).pack(side=tk.LEFT)

    def edit_source(self):
        """ویرایش منبع"""
        selected = self.sources_tree.selection()
        if not selected:
            messagebox.showwarning("هشدار", "لطفاً یک منبع انتخاب کنید")
            return

        # پیاده‌سازی ویرایش منبع
        messagebox.showinfo("اطلاع", "قابلیت ویرایش به زودی اضافه می‌شود")

    def delete_source(self):
        """حذف منبع"""
        selected = self.sources_tree.selection()
        if not selected:
            messagebox.showwarning("هشدار", "لطفاً یک منبع انتخاب کنید")
            return

        if messagebox.askyesno("تایید", "آیا مطمئن هستید که می‌خواهید این منبع را حذف کنید؟"):
            # پیاده‌سازی حذف منبع
            self.add_activity_log("منابع", "منبع حذف شد", "🗑️")
            self.update_sources_list()

    def test_source(self):
        """تست منبع"""
        selected = self.sources_tree.selection()
        if not selected:
            messagebox.showwarning("هشدار", "لطفاً یک منبع انتخاب کنید")
            return

        self.add_activity_log("تست", "در حال تست منبع...", "🔍")
        # پیاده‌سازی تست منبع
        messagebox.showinfo("تست", "منبع در دسترس است")

    def open_settings(self):
        """باز کردن پنجره تنظیمات"""
        def on_settings_close():
            # بارگذاری مجدد تنظیمات بعد از بستن پنجره تنظیمات
            if hasattr(self, 'bot_manager'):
                self.bot_manager.reload_settings()
            self.add_activity_log("سیستم", "تنظیمات به‌روزرسانی شد", "⚙️")

        settings_window = SettingsWindow(self.root)
        # اضافه کردن callback برای زمان بستن پنجره
        settings_window.window.protocol("WM_DELETE_WINDOW", lambda: [settings_window.window.destroy(), on_settings_close()])

    def open_stats(self):
        """باز کردن پنجره آمار تفصیلی"""
        stats_window = StatsWindow(self.root)

    def test_connection(self):
        """تست اتصال"""
        messagebox.showinfo("تست اتصال", "در حال تست اتصال...")

    def show_about(self):
        """نمایش درباره"""
        about_text = """
ربات هوشمند خبرهای کریپتوکارنسی

نسخه: 1.0.0
توسعه‌دهنده: تیم ربات کریپتو

این ربات به صورت خودکار خبرهای کریپتوکارنسی را
از منابع مختلف جمع‌آوری کرده و در X پست می‌کند.
        """
        messagebox.showinfo("درباره", about_text)

    def update_status(self):
        """به‌روزرسانی وضعیت"""
        try:
            # به‌روزرسانی آمار سریع
            stats = self.db.get_daily_statistics()

            news_count = stats.get('news_scraped', 0)
            posts_count = stats.get('tweets_posted', 0)
            crypto_count = stats.get('crypto_news_found', 0)

            self.news_today_label.config(text=str(news_count))
            self.posts_today_label.config(text=str(posts_count))
            self.crypto_news_label.config(text=str(crypto_count))

            # محاسبه نرخ موفقیت
            if crypto_count > 0:
                success_rate = (posts_count / crypto_count) * 100
                self.success_rate_label.config(text=f"{success_rate:.1f}%")

                # تغییر رنگ بر اساس نرخ موفقیت
                if success_rate >= 80:
                    color = "green"
                elif success_rate >= 50:
                    color = "orange"
                else:
                    color = "red"
                self.success_rate_label.config(foreground=color)
            else:
                self.success_rate_label.config(text="0%", foreground="gray")

            # به‌روزرسانی زمان آخرین بررسی
            if hasattr(self, 'bot_manager') and self.bot_manager:
                bot_status = self.bot_manager.get_status()
                last_check = bot_status['stats'].get('last_check_time')
                if last_check:
                    if isinstance(last_check, str):
                        from datetime import datetime
                        last_check = datetime.fromisoformat(last_check)
                    time_str = last_check.strftime("%H:%M:%S")
                    self.last_check_label.config(text=time_str)

            # به‌روزرسانی منابع
            self.update_sources_list()

            # به‌روزرسانی نمودار زنده
            if hasattr(self, 'realtime_stats'):
                self.realtime_stats.update_stats(news_count, crypto_count, posts_count)

            # شبیه‌سازی فعالیت برای نمایش
            if self.is_running:
                import random
                activities = [
                    ("استخراج", "بررسی منابع خبری جدید"),
                    ("تحلیل", "تشخیص محتوای کریپتو"),
                    ("فیلتر", "بررسی تکراری نبودن خبر"),
                    ("پست", "آماده‌سازی متن توییت"),
                    ("مانیتور", "بررسی عملکرد سیستم")
                ]

                if random.random() < 0.3:  # 30% احتمال اضافه کردن فعالیت جدید
                    activity_type, description = random.choice(activities)
                    self.add_activity_log(activity_type, description)

        except Exception as e:
            gui_logger.error(f"خطا در به‌روزرسانی وضعیت: {e}")

        # برنامه‌ریزی به‌روزرسانی بعدی
        self.root.after(3000, self.update_status)  # هر 3 ثانیه

    def update_sources_list(self):
        """به‌روزرسانی لیست منابع"""
        try:
            # پاک کردن لیست قبلی
            for item in self.sources_tree.get_children():
                self.sources_tree.delete(item)

            # دریافت منابع از دیتابیس
            sources = self.db.get_active_news_sources()

            for source in sources:
                status = "✅ فعال" if source.get('is_active') else "❌ غیرفعال"
                last_checked = source.get('last_checked', 'هرگز')
                if last_checked and last_checked != 'هرگز':
                    try:
                        # فرمت زمان
                        if isinstance(last_checked, str):
                            from datetime import datetime
                            dt = datetime.fromisoformat(last_checked.replace('Z', '+00:00'))
                            last_checked = dt.strftime("%H:%M:%S")
                    except:
                        pass

                self.sources_tree.insert('', tk.END, values=(
                    source.get('name', ''),
                    source.get('url', ''),
                    status,
                    last_checked
                ))

            # نمایش تعداد منابع
            count = len(sources)
            if hasattr(self, 'sources_count_label'):
                self.sources_count_label.config(text=f"تعداد منابع: {count}")

        except Exception as e:
            gui_logger.error(f"خطا در به‌روزرسانی منابع: {e}")

    def update_time(self):
        """به‌روزرسانی زمان"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=current_time)
        self.root.after(1000, self.update_time)  # هر ثانیه

    def on_closing(self):
        """رویداد بستن برنامه"""
        if self.is_running:
            if messagebox.askokcancel("خروج", "ربات در حال اجرا است. آیا مطمئن هستید؟"):
                self.stop_bot()
                self.root.destroy()
        else:
            self.root.destroy()

    def add_activity_log(self, activity_type: str, description: str, status: str = "✅"):
        """اضافه کردن فعالیت به لاگ"""
        timestamp = datetime.now().strftime("%H:%M:%S")

        # اضافه کردن به ابتدای لیست
        self.activity_tree.insert('', 0, values=(timestamp, activity_type, description, status))

        # محدود کردن تعداد آیتم‌ها
        items = self.activity_tree.get_children()
        if len(items) > 100:  # حداکثر 100 آیتم
            self.activity_tree.delete(items[-1])

        # اسکرول به بالا
        if items:
            self.activity_tree.see(items[0])

    def update_current_task(self, task: str, progress: float = 0):
        """به‌روزرسانی تسک فعلی"""
        self.current_task_label.config(text=task)
        self.progress_var.set(progress)
        self.progress_label.config(text=f"{progress:.1f}%")

        # تغییر رنگ بر اساس پیشرفت
        if progress < 30:
            color = "red"
        elif progress < 70:
            color = "orange"
        else:
            color = "green"

        self.current_task_label.config(foreground=color)

    def update_time_remaining(self, seconds: int):
        """به‌روزرسانی زمان باقی‌مانده"""
        if seconds > 0:
            minutes = seconds // 60
            secs = seconds % 60
            self.time_remaining_label.config(text=f"{minutes:02d}:{secs:02d}")
        else:
            self.time_remaining_label.config(text="--:--")

    def update_live_activity(self):
        """به‌روزرسانی فعالیت زنده"""
        try:
            if hasattr(self, 'bot_manager') and self.bot_manager:
                status = self.bot_manager.get_status()

                # به‌روزرسانی وضعیت فعلی
                if status['is_running']:
                    if status.get('current_task'):
                        self.update_current_task(
                            status['current_task'],
                            status.get('progress', 0)
                        )

                    # محاسبه زمان باقی‌مانده تا چرخه بعدی
                    next_cycle = status.get('next_cycle_in', 0)
                    self.update_time_remaining(next_cycle)

                    self.current_activity_label.config(text="🟢 فعال", foreground="green")
                else:
                    self.current_activity_label.config(text="🔴 متوقف", foreground="red")
                    self.update_current_task("متوقف", 0)
                    self.update_time_remaining(0)

        except Exception as e:
            gui_logger.error(f"خطا در به‌روزرسانی فعالیت زنده: {e}")

        # برنامه‌ریزی به‌روزرسانی بعدی
        self.root.after(1000, self.update_live_activity)  # هر ثانیه

    def clear_activity_log(self):
        """پاک کردن لاگ فعالیت"""
        for item in self.activity_tree.get_children():
            self.activity_tree.delete(item)

        self.add_activity_log("سیستم", "لاگ فعالیت پاک شد", "🗑️")

    def save_activity_report(self):
        """ذخیره گزارش فعالیت"""
        from tkinter import filedialog
        import csv

        filename = filedialog.asksaveasfilename(
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
            title="ذخیره گزارش فعالیت"
        )

        if filename:
            try:
                with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)
                    writer.writerow(["زمان", "نوع", "توضیحات", "وضعیت"])

                    for item in self.activity_tree.get_children():
                        values = self.activity_tree.item(item)['values']
                        writer.writerow(values)

                self.add_activity_log("سیستم", f"گزارش در {filename} ذخیره شد", "💾")
                messagebox.showinfo("موفقیت", "گزارش فعالیت ذخیره شد")

            except Exception as e:
                messagebox.showerror("خطا", f"خطا در ذخیره گزارش: {e}")

    def start_system_monitoring(self):
        """شروع مانیتورینگ سیستم"""
        self.system_start_time = datetime.now()
        self.update_system_status()

    def update_system_status(self):
        """به‌روزرسانی وضعیت سیستم"""
        try:
            # به‌روزرسانی وضعیت ربات
            if self.is_running:
                self.bot_status_label.config(text="🟢 فعال", foreground="green")
            else:
                self.bot_status_label.config(text="🔴 متوقف", foreground="red")

            # محاسبه زمان اجرا
            if hasattr(self, 'system_start_time'):
                uptime = datetime.now() - self.system_start_time
                hours = uptime.seconds // 3600
                minutes = (uptime.seconds % 3600) // 60
                seconds = uptime.seconds % 60
                self.uptime_label.config(text=f"{hours:02d}:{minutes:02d}:{seconds:02d}")

            # مانیتورینگ حافظه و CPU
            try:
                import psutil
                process = psutil.Process()

                # استفاده از حافظه
                memory_mb = process.memory_info().rss / 1024 / 1024
                self.memory_label.config(text=f"{memory_mb:.1f} MB")

                # استفاده از CPU
                cpu_percent = process.cpu_percent()
                self.cpu_label.config(text=f"{cpu_percent:.1f}%")

                # تعداد thread ها
                thread_count = process.num_threads()
                self.threads_label.config(text=str(thread_count))

            except ImportError:
                # اگر psutil نصب نباشد
                self.memory_label.config(text="N/A")
                self.cpu_label.config(text="N/A")
                self.threads_label.config(text="N/A")

            # تست اتصال اینترنت
            self.check_internet_connection()

        except Exception as e:
            gui_logger.error(f"خطا در به‌روزرسانی وضعیت سیستم: {e}")

        # برنامه‌ریزی به‌روزرسانی بعدی
        self.root.after(5000, self.update_system_status)  # هر 5 ثانیه

    def check_internet_connection(self):
        """بررسی اتصال اینترنت"""
        try:
            import requests
            response = requests.get("https://www.google.com", timeout=5)
            if response.status_code == 200:
                self.internet_status_label.config(text="🟢 متصل", foreground="green")
            else:
                self.internet_status_label.config(text="🟡 ضعیف", foreground="orange")
        except:
            self.internet_status_label.config(text="🔴 قطع", foreground="red")

    def test_connections(self):
        """تست اتصالات"""
        self.add_system_log("شروع تست اتصالات...", "INFO")

        # تست اتصال اینترنت
        try:
            import requests
            response = requests.get("https://www.google.com", timeout=10)
            if response.status_code == 200:
                self.add_system_log("✅ اتصال اینترنت: موفق", "SUCCESS")
            else:
                self.add_system_log("⚠️ اتصال اینترنت: ضعیف", "WARNING")
        except Exception as e:
            self.add_system_log(f"❌ اتصال اینترنت: ناموفق - {e}", "ERROR")

        # تست اتصال به X
        try:
            response = requests.get("https://twitter.com", timeout=10)
            if response.status_code == 200:
                self.add_system_log("✅ اتصال به X: موفق", "SUCCESS")
                self.x_status_label.config(text="🟢 متصل", foreground="green")
            else:
                self.add_system_log("⚠️ اتصال به X: مشکل دار", "WARNING")
                self.x_status_label.config(text="🟡 مشکل", foreground="orange")
        except Exception as e:
            self.add_system_log(f"❌ اتصال به X: ناموفق - {e}", "ERROR")
            self.x_status_label.config(text="🔴 قطع", foreground="red")

        # تست پایگاه داده
        try:
            stats = self.db.get_daily_statistics()
            self.add_system_log("✅ پایگاه داده: متصل و فعال", "SUCCESS")
            self.db_status_label.config(text="🟢 متصل", foreground="green")
        except Exception as e:
            self.add_system_log(f"❌ پایگاه داده: خطا - {e}", "ERROR")
            self.db_status_label.config(text="🔴 خطا", foreground="red")

    def add_system_log(self, message: str, level: str = "INFO"):
        """اضافه کردن لاگ سیستم"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.system_log_text.insert(tk.END, log_entry, level)
        self.system_log_text.see(tk.END)

    def clear_system_log(self):
        """پاک کردن لاگ سیستم"""
        self.system_log_text.delete(1.0, tk.END)
        self.add_system_log("لاگ سیستم پاک شد", "INFO")

    def reset_performance_stats(self):
        """بازنشانی آمار عملکرد"""
        self.system_start_time = datetime.now()
        self.add_system_log("آمار عملکرد بازنشانی شد", "INFO")

        # بازنشانی نمودار زنده
        if hasattr(self, 'realtime_stats'):
            self.realtime_stats.clear_stats()

    def run(self):
        """اجرای برنامه"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # بارگذاری تنظیمات سریع
        self.load_quick_settings()

        # اضافه کردن پیام خوش‌آمدگویی
        self.add_activity_log("سیستم", "ربات خبرهای کریپتو آماده است", "🚀")
        self.add_system_log("سیستم راه‌اندازی شد", "SUCCESS")

        self.root.mainloop()
