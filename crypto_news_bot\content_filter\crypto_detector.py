"""
سیستم تشخیص هوشمند محتوای کریپتوکارنسی
"""

import re
import string
from typing import Dict, List, Tuple, Set
from collections import Counter

# NLTK غیرفعال برای سادگی
NLTK_AVAILABLE = False

from crypto_news_bot.config import CRYPTO_KEYWORDS, CRYPTO_SYMBOLS
from crypto_news_bot.utils.logger import main_logger


class CryptoContentDetector:
    """کلاس تشخیص محتوای کریپتوکارنسی"""
    
    def __init__(self):
        self.crypto_keywords = set(CRYPTO_KEYWORDS)
        self.crypto_symbols = set(CRYPTO_SYMBOLS)

        if NLTK_AVAILABLE:
            self.stemmer = PorterStemmer()
            # دانلود داده‌های NLTK در صورت نیاز
            self._download_nltk_data()

            # کلمات توقف
            try:
                self.stop_words = set(stopwords.words('english'))
            except:
                self.stop_words = set()
        else:
            self.stemmer = None
            self.stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
        
        # الگوهای regex برای تشخیص
        self.price_pattern = re.compile(r'\$[\d,]+\.?\d*|\d+\.\d+\s*USD|USD\s*\d+')
        self.percentage_pattern = re.compile(r'[+-]?\d+\.?\d*%')
        self.hash_pattern = re.compile(r'#\w+')
        
        # کلمات کلیدی اضافی
        self.financial_terms = {
            'trading', 'investment', 'portfolio', 'market', 'price', 'volume',
            'bullish', 'bearish', 'pump', 'dump', 'moon', 'lambo', 'diamond hands',
            'paper hands', 'whale', 'dip', 'ath', 'support', 'resistance'
        }
        
        # کلمات منفی (کاهش امتیاز)
        self.negative_terms = {
            'scam', 'fraud', 'fake', 'spam', 'bot', 'advertisement', 'promotion'
        }
    
    def _download_nltk_data(self):
        """دانلود داده‌های مورد نیاز NLTK"""
        if not NLTK_AVAILABLE:
            return

        try:
            nltk.data.find('tokenizers/punkt')
        except LookupError:
            try:
                nltk.download('punkt', quiet=True)
            except:
                pass

        try:
            nltk.data.find('corpora/stopwords')
        except LookupError:
            try:
                nltk.download('stopwords', quiet=True)
            except:
                pass
    
    def preprocess_text(self, text: str) -> List[str]:
        """پیش‌پردازش متن"""
        # تبدیل به حروف کوچک
        text = text.lower()
        
        # حذف علائم نگارشی (به جز # و $)
        text = re.sub(r'[^\w\s#$%+-]', ' ', text)
        
        # توکن‌سازی
        if NLTK_AVAILABLE:
            try:
                tokens = word_tokenize(text)
            except:
                tokens = text.split()
        else:
            tokens = text.split()
        
        # حذف کلمات توقف و کلمات کوتاه
        tokens = [token for token in tokens 
                 if token not in self.stop_words and len(token) > 2]
        
        return tokens
    
    def extract_crypto_features(self, text: str) -> Dict[str, float]:
        """استخراج ویژگی‌های کریپتو از متن"""
        features = {
            'keyword_score': 0.0,
            'symbol_score': 0.0,
            'price_mentions': 0.0,
            'percentage_mentions': 0.0,
            'hashtag_score': 0.0,
            'financial_terms_score': 0.0,
            'negative_score': 0.0,
            'length_score': 0.0
        }
        
        # پیش‌پردازش
        tokens = self.preprocess_text(text)
        text_lower = text.lower()
        
        # امتیاز کلمات کلیدی
        keyword_matches = sum(1 for keyword in self.crypto_keywords 
                            if keyword in text_lower)
        features['keyword_score'] = min(keyword_matches / 3.0, 1.0)
        
        # امتیاز نمادهای کریپتو
        symbol_matches = sum(1 for symbol in self.crypto_symbols 
                           if symbol.lower() in text_lower)
        features['symbol_score'] = min(symbol_matches / 2.0, 1.0)
        
        # تشخیص قیمت
        price_matches = len(self.price_pattern.findall(text))
        features['price_mentions'] = min(price_matches / 2.0, 1.0)
        
        # تشخیص درصد
        percentage_matches = len(self.percentage_pattern.findall(text))
        features['percentage_mentions'] = min(percentage_matches / 2.0, 1.0)
        
        # امتیاز هشتگ‌ها
        hashtags = self.hash_pattern.findall(text_lower)
        crypto_hashtags = sum(1 for tag in hashtags 
                            if any(keyword in tag for keyword in self.crypto_keywords))
        features['hashtag_score'] = min(crypto_hashtags / 2.0, 1.0)
        
        # اصطلاحات مالی
        financial_matches = sum(1 for term in self.financial_terms 
                              if term in text_lower)
        features['financial_terms_score'] = min(financial_matches / 3.0, 1.0)
        
        # کلمات منفی
        negative_matches = sum(1 for term in self.negative_terms 
                             if term in text_lower)
        features['negative_score'] = min(negative_matches / 2.0, 1.0)
        
        # امتیاز طول متن
        word_count = len(tokens)
        if 10 <= word_count <= 100:
            features['length_score'] = 1.0
        elif word_count < 10:
            features['length_score'] = word_count / 10.0
        else:
            features['length_score'] = max(0.5, 100.0 / word_count)
        
        return features
    
    def calculate_crypto_score(self, text: str) -> Tuple[bool, float]:
        """محاسبه امتیاز کریپتو برای متن"""
        features = self.extract_crypto_features(text)
        
        # وزن‌های مختلف برای ویژگی‌ها
        weights = {
            'keyword_score': 0.25,
            'symbol_score': 0.20,
            'price_mentions': 0.15,
            'percentage_mentions': 0.10,
            'hashtag_score': 0.10,
            'financial_terms_score': 0.15,
            'length_score': 0.05
        }
        
        # محاسبه امتیاز کل
        total_score = sum(features[key] * weights[key] 
                         for key in weights.keys())
        
        # کاهش امتیاز برای کلمات منفی
        total_score -= features['negative_score'] * 0.3
        
        # اطمینان از بازه [0, 1]
        total_score = max(0.0, min(1.0, total_score))
        
        # تشخیص نهایی
        is_crypto = total_score >= 0.3
        
        return is_crypto, total_score
    
    def analyze_content_quality(self, text: str) -> Dict[str, float]:
        """تحلیل کیفیت محتوا"""
        quality_metrics = {
            'readability': 0.0,
            'informativeness': 0.0,
            'spam_likelihood': 0.0,
            'engagement_potential': 0.0
        }
        
        tokens = self.preprocess_text(text)
        word_count = len(tokens)
        
        # خوانایی (بر اساس طول جملات و کلمات)
        sentences = text.split('.')
        avg_sentence_length = sum(len(s.split()) for s in sentences) / max(len(sentences), 1)
        quality_metrics['readability'] = min(1.0, 20.0 / max(avg_sentence_length, 1))
        
        # اطلاعاتی بودن (بر اساس تنوع کلمات)
        unique_words = len(set(tokens))
        if word_count > 0:
            diversity_ratio = unique_words / word_count
            quality_metrics['informativeness'] = min(1.0, diversity_ratio * 2)
        
        # احتمال اسپم
        spam_indicators = [
            text.count('!') > 3,
            text.count('?') > 2,
            len(re.findall(r'[A-Z]{3,}', text)) > 2,
            'click' in text.lower(),
            'buy now' in text.lower(),
            'limited time' in text.lower()
        ]
        quality_metrics['spam_likelihood'] = sum(spam_indicators) / len(spam_indicators)
        
        # پتانسیل تعامل
        engagement_words = {'new', 'breaking', 'update', 'analysis', 'prediction', 'trend'}
        engagement_count = sum(1 for word in engagement_words if word in text.lower())
        quality_metrics['engagement_potential'] = min(1.0, engagement_count / 3.0)
        
        return quality_metrics
