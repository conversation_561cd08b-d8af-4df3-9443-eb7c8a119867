#!/usr/bin/env python3
"""
اسکریپت نصب و راه‌اندازی ربات خبرهای کریپتو
"""

import os
import sys
import subprocess
import platform
from pathlib import Path


def check_python_version():
    """بررسی نسخه پایتون"""
    if sys.version_info < (3, 8):
        print("❌ نسخه پایتون 3.8 یا بالاتر مورد نیاز است")
        print(f"نسخه فعلی: {sys.version}")
        return False
    
    print(f"✅ نسخه پایتون: {sys.version}")
    return True


def check_system_requirements():
    """بررسی نیازمندی‌های سیستم"""
    print("🔍 بررسی نیازمندی‌های سیستم...")
    
    # بررسی سیستم عامل
    os_name = platform.system()
    print(f"سیستم عامل: {os_name}")
    
    # بررسی Chrome
    chrome_paths = {
        'Windows': [
            r'C:\Program Files\Google\Chrome\Application\chrome.exe',
            r'C:\Program Files (x86)\Google\Chrome\Application\chrome.exe'
        ],
        'Darwin': ['/Applications/Google Chrome.app/Contents/MacOS/Google Chrome'],
        'Linux': ['/usr/bin/google-chrome', '/usr/bin/chromium-browser']
    }
    
    chrome_found = False
    if os_name in chrome_paths:
        for path in chrome_paths[os_name]:
            if os.path.exists(path):
                chrome_found = True
                print(f"✅ Chrome یافت شد: {path}")
                break
    
    if not chrome_found:
        print("⚠️  Google Chrome یافت نشد. لطفاً Chrome را نصب کنید.")
        print("دانلود: https://www.google.com/chrome/")
    
    return chrome_found


def create_virtual_environment():
    """ایجاد محیط مجازی"""
    print("🔧 ایجاد محیط مجازی...")
    
    venv_path = Path("venv")
    
    if venv_path.exists():
        print("✅ محیط مجازی موجود است")
        return True
    
    try:
        subprocess.run([sys.executable, "-m", "venv", "venv"], check=True)
        print("✅ محیط مجازی ایجاد شد")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ خطا در ایجاد محیط مجازی: {e}")
        return False


def install_requirements():
    """نصب وابستگی‌ها"""
    print("📦 نصب وابستگی‌ها...")
    
    # تعیین مسیر pip در محیط مجازی
    if platform.system() == "Windows":
        pip_path = Path("venv/Scripts/pip.exe")
    else:
        pip_path = Path("venv/bin/pip")
    
    if not pip_path.exists():
        print("❌ pip در محیط مجازی یافت نشد")
        return False
    
    try:
        # به‌روزرسانی pip
        subprocess.run([str(pip_path), "install", "--upgrade", "pip"], check=True)
        
        # نصب وابستگی‌ها
        subprocess.run([str(pip_path), "install", "-r", "requirements.txt"], check=True)
        
        print("✅ وابستگی‌ها نصب شدند")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ خطا در نصب وابستگی‌ها: {e}")
        return False


def download_nltk_data():
    """دانلود داده‌های NLTK"""
    print("📚 دانلود داده‌های NLTK...")
    
    if platform.system() == "Windows":
        python_path = Path("venv/Scripts/python.exe")
    else:
        python_path = Path("venv/bin/python")
    
    try:
        nltk_script = """
import nltk
import ssl

try:
    _create_unverified_https_context = ssl._create_unverified_context
except AttributeError:
    pass
else:
    ssl._create_default_https_context = _create_unverified_https_context

nltk.download('punkt', quiet=True)
nltk.download('stopwords', quiet=True)
print("NLTK data downloaded successfully")
"""
        
        result = subprocess.run([str(python_path), "-c", nltk_script], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ داده‌های NLTK دانلود شدند")
            return True
        else:
            print(f"⚠️  خطا در دانلود NLTK: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ خطا در دانلود داده‌های NLTK: {e}")
        return False


def create_env_file():
    """ایجاد فایل .env"""
    print("⚙️  ایجاد فایل تنظیمات...")
    
    env_file = Path(".env")
    
    if env_file.exists():
        print("✅ فایل .env موجود است")
        return True
    
    try:
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write("""# تنظیمات X (Twitter)
X_USERNAME=your_username
X_PASSWORD=your_password
X_EMAIL=<EMAIL>

# تنظیمات اختیاری
LOG_LEVEL=INFO
SCRAPING_INTERVAL=5
MAX_RETRIES=3
""")
        
        print("✅ فایل .env ایجاد شد")
        print("⚠️  لطفاً اطلاعات حساب X خود را در فایل .env وارد کنید")
        return True
        
    except Exception as e:
        print(f"❌ خطا در ایجاد فایل .env: {e}")
        return False


def create_desktop_shortcut():
    """ایجاد میانبر روی دسکتاپ"""
    print("🔗 ایجاد میانبر...")
    
    try:
        if platform.system() == "Windows":
            # ایجاد فایل batch برای ویندوز
            batch_content = f"""@echo off
cd /d "{os.getcwd()}"
venv\\Scripts\\python.exe main.py
pause
"""
            with open("run_crypto_bot.bat", 'w') as f:
                f.write(batch_content)
            
            print("✅ فایل run_crypto_bot.bat ایجاد شد")
            
        else:
            # ایجاد اسکریپت shell برای Linux/Mac
            shell_content = f"""#!/bin/bash
cd "{os.getcwd()}"
./venv/bin/python main.py
"""
            with open("run_crypto_bot.sh", 'w') as f:
                f.write(shell_content)
            
            # اجازه اجرا
            os.chmod("run_crypto_bot.sh", 0o755)
            print("✅ فایل run_crypto_bot.sh ایجاد شد")
        
        return True
        
    except Exception as e:
        print(f"❌ خطا در ایجاد میانبر: {e}")
        return False


def main():
    """تابع اصلی نصب"""
    print("🤖 نصب ربات هوشمند خبرهای کریپتوکارنسی")
    print("=" * 50)
    
    # بررسی نیازمندی‌ها
    if not check_python_version():
        return False
    
    if not check_system_requirements():
        print("⚠️  برخی نیازمندی‌ها یافت نشدند، اما نصب ادامه می‌یابد")
    
    # مراحل نصب
    steps = [
        ("ایجاد محیط مجازی", create_virtual_environment),
        ("نصب وابستگی‌ها", install_requirements),
        ("دانلود داده‌های NLTK", download_nltk_data),
        ("ایجاد فایل تنظیمات", create_env_file),
        ("ایجاد میانبر", create_desktop_shortcut)
    ]
    
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}...")
        if not step_func():
            print(f"❌ خطا در {step_name}")
            return False
    
    print("\n" + "=" * 50)
    print("🎉 نصب با موفقیت تکمیل شد!")
    print("\n📝 مراحل بعدی:")
    print("1. فایل .env را ویرایش کرده و اطلاعات حساب X خود را وارد کنید")
    print("2. برای اجرای برنامه:")
    
    if platform.system() == "Windows":
        print("   - روی فایل run_crypto_bot.bat دوبار کلیک کنید")
        print("   - یا در terminal: python main.py")
    else:
        print("   - ./run_crypto_bot.sh")
        print("   - یا: python main.py")
    
    print("\n⚠️  نکات مهم:")
    print("- از این ربات مطابق با قوانین X استفاده کنید")
    print("- اطلاعات حساب خود را محرمانه نگه دارید")
    print("- برای پشتیبانی، مستندات را مطالعه کنید")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
