"""
پنجره تنظیمات
"""

import tkinter as tk
from tkinter import ttk, messagebox
from crypto_news_bot.database.models import DatabaseManager


class SettingsWindow:
    """پنجره تنظیمات"""
    
    def __init__(self, parent):
        self.parent = parent
        self.db = DatabaseManager()
        
        self.window = tk.Toplevel(parent)
        self.window.title("تنظیمات")
        self.window.geometry("600x500")
        self.window.resizable(False, False)
        self.window.transient(parent)
        self.window.grab_set()
        
        self.setup_ui()
        self.load_settings()
    
    def setup_ui(self):
        """راه‌اندازی رابط کاربری"""
        notebook = ttk.Notebook(self.window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # تب تنظیمات عمومی
        self.create_general_tab(notebook)
        
        # تب تنظیمات X
        self.create_x_tab(notebook)
        
        # تب تنظیمات کریپتو
        self.create_crypto_tab(notebook)
        
        # دکمه‌های کنترل
        buttons_frame = ttk.Frame(self.window)
        buttons_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(buttons_frame, text="ذخیره", command=self.save_settings).pack(side=tk.RIGHT, padx=5)
        ttk.Button(buttons_frame, text="لغو", command=self.window.destroy).pack(side=tk.RIGHT)
        ttk.Button(buttons_frame, text="بازنشانی", command=self.reset_settings).pack(side=tk.LEFT)
    
    def create_general_tab(self, notebook):
        """ایجاد تب تنظیمات عمومی"""
        general_frame = ttk.Frame(notebook)
        notebook.add(general_frame, text="عمومی")
        
        # تنظیمات زمان‌بندی
        timing_group = ttk.LabelFrame(general_frame, text="زمان‌بندی", padding=10)
        timing_group.pack(fill=tk.X, padx=10, pady=5)
        
        # فاصله بررسی
        ttk.Label(timing_group, text="فاصله بررسی خبرها (دقیقه):").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.check_interval_var = tk.StringVar(value="5")
        ttk.Spinbox(timing_group, from_=1, to=60, textvariable=self.check_interval_var, width=10).grid(row=0, column=1, sticky=tk.E, pady=2)
        
        # حداکثر پست روزانه
        ttk.Label(timing_group, text="حداکثر پست روزانه:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.max_posts_var = tk.StringVar(value="50")
        ttk.Spinbox(timing_group, from_=1, to=200, textvariable=self.max_posts_var, width=10).grid(row=1, column=1, sticky=tk.E, pady=2)
        
        # فاصله بین پست‌ها
        ttk.Label(timing_group, text="فاصله بین پست‌ها (دقیقه):").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.post_interval_var = tk.StringVar(value="5")
        ttk.Spinbox(timing_group, from_=1, to=30, textvariable=self.post_interval_var, width=10).grid(row=2, column=1, sticky=tk.E, pady=2)
        
        timing_group.columnconfigure(1, weight=1)
        
        # تنظیمات کیفیت
        quality_group = ttk.LabelFrame(general_frame, text="کیفیت محتوا", padding=10)
        quality_group.pack(fill=tk.X, padx=10, pady=5)
        
        # حداقل امتیاز کریپتو
        ttk.Label(quality_group, text="حداقل امتیاز کریپتو:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.min_crypto_score_var = tk.DoubleVar(value=0.3)
        ttk.Scale(quality_group, from_=0.0, to=1.0, variable=self.min_crypto_score_var, 
                 orient=tk.HORIZONTAL).grid(row=0, column=1, sticky=tk.EW, pady=2)
        
        # آستانه شباهت
        ttk.Label(quality_group, text="آستانه تشخیص تکرار:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.similarity_threshold_var = tk.DoubleVar(value=0.8)
        ttk.Scale(quality_group, from_=0.0, to=1.0, variable=self.similarity_threshold_var,
                 orient=tk.HORIZONTAL).grid(row=1, column=1, sticky=tk.EW, pady=2)
        
        quality_group.columnconfigure(1, weight=1)
    
    def create_x_tab(self, notebook):
        """ایجاد تب تنظیمات X"""
        x_frame = ttk.Frame(notebook)
        notebook.add(x_frame, text="حساب X")
        
        # اطلاعات حساب
        account_group = ttk.LabelFrame(x_frame, text="اطلاعات حساب", padding=10)
        account_group.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(account_group, text="نام کاربری:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.username_var = tk.StringVar()
        ttk.Entry(account_group, textvariable=self.username_var, width=30).grid(row=0, column=1, sticky=tk.EW, pady=2)
        
        ttk.Label(account_group, text="ایمیل:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.email_var = tk.StringVar()
        ttk.Entry(account_group, textvariable=self.email_var, width=30).grid(row=1, column=1, sticky=tk.EW, pady=2)
        
        ttk.Label(account_group, text="رمز عبور:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.password_var = tk.StringVar()
        ttk.Entry(account_group, textvariable=self.password_var, show="*", width=30).grid(row=2, column=1, sticky=tk.EW, pady=2)
        
        account_group.columnconfigure(1, weight=1)
        
        # تنظیمات امنیتی
        security_group = ttk.LabelFrame(x_frame, text="تنظیمات امنیتی", padding=10)
        security_group.pack(fill=tk.X, padx=10, pady=5)
        
        self.use_proxy_var = tk.BooleanVar()
        ttk.Checkbutton(security_group, text="استفاده از پروکسی", 
                       variable=self.use_proxy_var).pack(anchor=tk.W)
        
        self.random_delay_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(security_group, text="تاخیر تصادفی بین عملیات", 
                       variable=self.random_delay_var).pack(anchor=tk.W)
        
        self.headless_mode_var = tk.BooleanVar()
        ttk.Checkbutton(security_group, text="حالت مخفی (Headless)", 
                       variable=self.headless_mode_var).pack(anchor=tk.W)
    
    def create_crypto_tab(self, notebook):
        """ایجاد تب تنظیمات کریپتو"""
        crypto_frame = ttk.Frame(notebook)
        notebook.add(crypto_frame, text="کریپتو")
        
        # کلمات کلیدی
        keywords_group = ttk.LabelFrame(crypto_frame, text="کلمات کلیدی", padding=10)
        keywords_group.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        ttk.Label(keywords_group, text="کلمات کلیدی (هر خط یک کلمه):").pack(anchor=tk.W)
        
        self.keywords_text = tk.Text(keywords_group, height=8, wrap=tk.WORD)
        keywords_scrollbar = ttk.Scrollbar(keywords_group, orient=tk.VERTICAL, command=self.keywords_text.yview)
        self.keywords_text.configure(yscrollcommand=keywords_scrollbar.set)
        
        self.keywords_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        keywords_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # نمادهای کریپتو
        symbols_group = ttk.LabelFrame(crypto_frame, text="نمادهای کریپتو", padding=10)
        symbols_group.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        ttk.Label(symbols_group, text="نمادهای کریپتو (جدا شده با کاما):").pack(anchor=tk.W)
        
        self.symbols_text = tk.Text(symbols_group, height=4, wrap=tk.WORD)
        symbols_scrollbar = ttk.Scrollbar(symbols_group, orient=tk.VERTICAL, command=self.symbols_text.yview)
        self.symbols_text.configure(yscrollcommand=symbols_scrollbar.set)
        
        self.symbols_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        symbols_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def load_settings(self):
        """بارگذاری تنظیمات"""
        try:
            # بارگذاری از دیتابیس
            self.check_interval_var.set(self.db.get_setting("check_interval", "5"))
            self.max_posts_var.set(self.db.get_setting("max_posts_daily", "50"))
            self.post_interval_var.set(self.db.get_setting("post_interval", "5"))

            self.min_crypto_score_var.set(float(self.db.get_setting("min_crypto_score", "0.3")))
            self.similarity_threshold_var.set(float(self.db.get_setting("similarity_threshold", "0.8")))

            self.username_var.set(self.db.get_setting("x_username", ""))
            self.email_var.set(self.db.get_setting("x_email", ""))
            # رمز عبور را از متغیر محیطی بخوانید

            self.use_proxy_var.set(bool(int(self.db.get_setting("use_proxy", "0"))))
            self.random_delay_var.set(bool(int(self.db.get_setting("random_delay", "1"))))
            self.headless_mode_var.set(bool(int(self.db.get_setting("headless_mode", "0"))))

            # کلمات کلیدی - بارگذاری پیش‌فرض در صورت خالی بودن
            keywords = self.db.get_setting("crypto_keywords", "")
            if not keywords:
                # کلمات پیش‌فرض
                default_keywords = [
                    "bitcoin", "btc", "ethereum", "eth", "crypto", "cryptocurrency",
                    "blockchain", "defi", "nft", "altcoin", "trading", "hodl",
                    "بیت کوین", "اتریوم", "کریپتو", "رمزارز", "بلاک چین", "ارز دیجیتال"
                ]
                keywords = "\n".join(default_keywords)
                # ذخیره پیش‌فرض
                self.db.set_setting("crypto_keywords", ",".join(default_keywords))
            else:
                keywords = keywords.replace(",", "\n")

            self.keywords_text.insert(tk.END, keywords)

            # نمادها - بارگذاری پیش‌فرض در صورت خالی بودن
            symbols = self.db.get_setting("crypto_symbols", "")
            if not symbols:
                # نمادهای پیش‌فرض
                default_symbols = "BTC, ETH, BNB, XRP, ADA, SOL, DOGE, DOT, AVAX, SHIB, MATIC, LTC, UNI, LINK, ATOM, XLM, ALGO, VET, ICP, FIL"
                symbols = default_symbols
                # ذخیره پیش‌فرض
                self.db.set_setting("crypto_symbols", default_symbols)

            self.symbols_text.insert(tk.END, symbols)

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در بارگذاری تنظیمات: {e}")
    
    def save_settings(self):
        """ذخیره تنظیمات"""
        try:
            # ذخیره در دیتابیس
            self.db.set_setting("check_interval", self.check_interval_var.get())
            self.db.set_setting("max_posts_daily", self.max_posts_var.get())
            self.db.set_setting("post_interval", self.post_interval_var.get())
            
            self.db.set_setting("min_crypto_score", str(self.min_crypto_score_var.get()))
            self.db.set_setting("similarity_threshold", str(self.similarity_threshold_var.get()))
            
            self.db.set_setting("x_username", self.username_var.get())
            self.db.set_setting("x_email", self.email_var.get())
            
            self.db.set_setting("use_proxy", str(int(self.use_proxy_var.get())))
            self.db.set_setting("random_delay", str(int(self.random_delay_var.get())))
            self.db.set_setting("headless_mode", str(int(self.headless_mode_var.get())))
            
            # کلمات کلیدی
            keywords = self.keywords_text.get(1.0, tk.END).strip()
            keywords_list = [k.strip() for k in keywords.split("\n") if k.strip()]
            self.db.set_setting("crypto_keywords", ",".join(keywords_list))
            
            # نمادها
            symbols = self.symbols_text.get(1.0, tk.END).strip()
            self.db.set_setting("crypto_symbols", symbols)
            
            messagebox.showinfo("موفقیت", "✅ تنظیمات با موفقیت ذخیره شد!\n\nتغییرات در اجرای بعدی ربات اعمال می‌شوند.")
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در ذخیره تنظیمات: {e}")
    
    def reset_settings(self):
        """بازنشانی تنظیمات"""
        if messagebox.askyesno("تایید", "آیا مطمئن هستید که می‌خواهید تنظیمات را بازنشانی کنید؟"):
            # بازنشانی به مقادیر پیش‌فرض
            self.check_interval_var.set("5")
            self.max_posts_var.set("50")
            self.post_interval_var.set("5")
            self.min_crypto_score_var.set(0.3)
            self.similarity_threshold_var.set(0.8)
            
            self.username_var.set("")
            self.email_var.set("")
            self.password_var.set("")
            
            self.use_proxy_var.set(False)
            self.random_delay_var.set(True)
            self.headless_mode_var.set(False)
            
            self.keywords_text.delete(1.0, tk.END)
            self.symbols_text.delete(1.0, tk.END)
