#!/usr/bin/env python3
"""
ربات هوشمند خبرهای کریپتوکارنسی برای X
نقطه ورود اصلی برنامه
"""

import sys
import os
from pathlib import Path

# اضافه کردن مسیر پروژه به Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from crypto_news_bot.utils.logger import main_logger
from crypto_news_bot.gui.main_window import CryptoBotGUI
from crypto_news_bot.database.models import DatabaseManager


def main():
    """تابع اصلی برنامه"""
    try:
        main_logger.info("شروع ربات خبرهای کریپتو...")

        # بررسی و ایجاد پایگاه داده
        db = DatabaseManager()
        main_logger.info("پایگاه داده آماده است")

        # راه‌اندازی رابط گرافیکی
        app = CryptoBotGUI()
        app.run()

    except KeyboardInterrupt:
        main_logger.info("برنامه توسط کاربر متوقف شد")
    except Exception as e:
        import traceback
        main_logger.error(f"خطا در اجرای برنامه: {e}")
        main_logger.error(f"جزئیات خطا: {traceback.format_exc()}")
        print(f"خطای تفصیلی: {traceback.format_exc()}")
        sys.exit(1)


if __name__ == "__main__":
    main()
