"""
تنظیمات و پیکربندی ربات
"""

import os
from pathlib import Path
from dotenv import load_dotenv

# بارگذاری متغیرهای محیطی
load_dotenv()

# مسیرهای پروژه
PROJECT_ROOT = Path(__file__).parent.parent
DATA_DIR = PROJECT_ROOT / "data"
LOGS_DIR = PROJECT_ROOT / "logs"
CONFIG_DIR = PROJECT_ROOT / "config"

# ایجاد پوشه‌های مورد نیاز
DATA_DIR.mkdir(exist_ok=True)
LOGS_DIR.mkdir(exist_ok=True)
CONFIG_DIR.mkdir(exist_ok=True)

# تنظیمات پایگاه داده
DATABASE_PATH = DATA_DIR / "crypto_bot.db"

# تنظیمات X (Twitter)
X_USERNAME = os.getenv("X_USERNAME", "")
X_PASSWORD = os.getenv("X_PASSWORD", "")
X_EMAIL = os.getenv("X_EMAIL", "")

# تنظیمات Web Scraping
SCRAPING_INTERVAL = 5  # دقیقه
REQUEST_TIMEOUT = 30  # ثانیه
MAX_RETRIES = 3
DELAY_BETWEEN_REQUESTS = 2  # ثانیه

# تنظیمات تشخیص کریپتو
CRYPTO_KEYWORDS = [
    "bitcoin", "btc", "ethereum", "eth", "crypto", "cryptocurrency", 
    "blockchain", "defi", "nft", "altcoin", "trading", "hodl"
    
]

CRYPTO_SYMBOLS = [
    "BTC", "ETH", "BNB", "XRP", "ADA", "SOL", "DOGE", "DOT", "AVAX", "SHIB",
    "MATIC", "LTC", "UNI", "LINK", "ATOM", "XLM", "ALGO", "VET", "ICP", "FIL"
]

# تنظیمات لاگ
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# تنظیمات GUI
WINDOW_TITLE = "ربات خبرهای کریپتو"
WINDOW_SIZE = (1200, 800)
THEME = "light"  # light, dark

# تنظیمات امنیتی
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
]

# صفحات خبری پیش‌فرض (قابل تغییر توسط کاربر)
DEFAULT_NEWS_SOURCES = [
    "https://x.com/cointelegraph",
    "https://x.com/coindesk",
    "https://x.com/bitcoinmagazine",
    "https://x.com/cryptonews",
    "https://x.com/decrypt_co"
]
