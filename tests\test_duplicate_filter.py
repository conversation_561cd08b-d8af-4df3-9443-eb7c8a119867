"""
تست‌های فیلتر محتوای تکراری
"""

import unittest
import sys
from pathlib import Path

# اضافه کردن مسیر پروژه
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from crypto_news_bot.content_filter.duplicate_filter import DuplicateContentFilter, ContentQualityFilter


class TestDuplicateFilter(unittest.TestCase):
    """تست‌های فیلتر تکراری"""
    
    def setUp(self):
        """راه‌اندازی تست"""
        self.filter = DuplicateContentFilter(similarity_threshold=0.8)
    
    def test_exact_duplicate(self):
        """تست تشخیص تکرار دقیق"""
        text = "Bitcoin price reaches new all-time high today"
        
        # اولین بار نباید تکراری باشد
        is_duplicate, similarity, reason = self.filter.is_duplicate(text, check_history=False)
        self.assertFalse(is_duplicate)
        
        # اضافه کردن به تاریخچه
        self.filter.add_content(text)
        
        # دومین بار باید تکراری باشد
        is_duplicate, similarity, reason = self.filter.is_duplicate(text)
        self.assertTrue(is_duplicate)
        self.assertEqual(similarity, 1.0)
        self.assertEqual(reason, "exact_hash_match")
    
    def test_similar_content(self):
        """تست تشخیص محتوای مشابه"""
        text1 = "Bitcoin price reaches new all-time high of $50,000"
        text2 = "Bitcoin price hits new all-time high at $50,000"
        
        # اضافه کردن اولین متن
        self.filter.add_content(text1)
        
        # بررسی شباهت
        is_duplicate, similarity, reason = self.filter.is_duplicate(text2)
        
        # باید مشابه تشخیص داده شود
        self.assertTrue(is_duplicate)
        self.assertGreater(similarity, 0.8)
    
    def test_different_content(self):
        """تست محتوای متفاوت"""
        text1 = "Bitcoin price analysis for today"
        text2 = "Ethereum smart contract development guide"
        
        # اضافه کردن اولین متن
        self.filter.add_content(text1)
        
        # بررسی تفاوت
        is_duplicate, similarity, reason = self.filter.is_duplicate(text2)
        
        # نباید مشابه باشد
        self.assertFalse(is_duplicate)
        self.assertLess(similarity, 0.8)
    
    def test_normalize_text(self):
        """تست نرمال‌سازی متن"""
        text1 = "Bitcoin price: $50,000 (up 10%) #crypto @user https://example.com"
        text2 = "Bitcoin price:  (up %) #  @ "
        
        normalized1 = self.filter.normalize_text(text1)
        normalized2 = self.filter.normalize_text(text2)
        
        # متن‌های نرمال شده باید مشابه باشند
        self.assertEqual(normalized1, normalized2)
    
    def test_fingerprint_generation(self):
        """تست تولید fingerprint"""
        text = "Bitcoin price reaches new high"
        fingerprint = self.filter.generate_fingerprint(text)
        
        # باید شامل n-gram ها باشد
        self.assertIsInstance(fingerprint, list)
        self.assertGreater(len(fingerprint), 0)
        
        # بررسی وجود 2-gram و 3-gram
        self.assertIn("bitcoin_price", fingerprint)
        self.assertIn("price_reaches", fingerprint)
    
    def test_similarity_calculation(self):
        """تست محاسبه شباهت"""
        text1 = "Bitcoin price goes up today"
        text2 = "Bitcoin price goes down today"
        text3 = "Ethereum smart contract update"
        
        # شباهت بالا
        similarity1 = self.filter.calculate_similarity(text1, text2)
        self.assertGreater(similarity1, 0.7)
        
        # شباهت پایین
        similarity2 = self.filter.calculate_similarity(text1, text3)
        self.assertLess(similarity2, 0.3)
    
    def test_threshold_update(self):
        """تست به‌روزرسانی آستانه"""
        # آستانه معتبر
        result1 = self.filter.update_threshold(0.5)
        self.assertTrue(result1)
        self.assertEqual(self.filter.similarity_threshold, 0.5)
        
        # آستانه نامعتبر
        result2 = self.filter.update_threshold(1.5)
        self.assertFalse(result2)
        self.assertEqual(self.filter.similarity_threshold, 0.5)  # باید تغییر نکند
    
    def test_content_history_limit(self):
        """تست محدودیت تاریخچه"""
        # تنظیم حد پایین برای تست
        self.filter.max_history = 5
        
        # اضافه کردن محتوای بیشتر از حد
        for i in range(10):
            self.filter.add_content(f"Test content number {i}")
        
        # باید فقط 5 محتوا باقی بماند
        self.assertEqual(len(self.filter.content_fingerprints), 5)
        self.assertEqual(len(self.filter.content_hashes), 5)
    
    def test_statistics(self):
        """تست آمار فیلتر"""
        # اضافه کردن چند محتوا
        for i in range(3):
            self.filter.add_content(f"Content {i}")
        
        stats = self.filter.get_statistics()
        
        self.assertEqual(stats['total_content'], 3)
        self.assertEqual(stats['unique_hashes'], 3)
        self.assertIsInstance(stats['similarity_threshold'], float)


class TestContentQualityFilter(unittest.TestCase):
    """تست‌های فیلتر کیفیت محتوا"""
    
    def setUp(self):
        self.quality_filter = ContentQualityFilter()
    
    def test_quality_content(self):
        """تست محتوای با کیفیت"""
        quality_text = "Bitcoin's latest network upgrade improves transaction speed and reduces fees."
        
        is_quality, issues = self.quality_filter.check_quality(quality_text)
        
        self.assertTrue(is_quality)
        self.assertEqual(len(issues), 0)
    
    def test_short_content(self):
        """تست محتوای کوتاه"""
        short_text = "BTC up"
        
        is_quality, issues = self.quality_filter.check_quality(short_text)
        
        self.assertFalse(is_quality)
        self.assertGreater(len(issues), 0)
        self.assertTrue(any("کوتاه" in issue for issue in issues))
    
    def test_long_content(self):
        """تست محتوای بلند"""
        long_text = "Bitcoin " * 100  # خیلی بلند
        
        is_quality, issues = self.quality_filter.check_quality(long_text)
        
        self.assertFalse(is_quality)
        self.assertTrue(any("بلند" in issue for issue in issues))
    
    def test_spam_content(self):
        """تست محتوای اسپم"""
        spam_text = "BUY NOW! Limited time offer! Click here for free money!"
        
        is_quality, issues = self.quality_filter.check_quality(spam_text)
        
        self.assertFalse(is_quality)
        self.assertTrue(any("اسپم" in issue for issue in issues))
    
    def test_excessive_caps(self):
        """تست استفاده بیش از حد از حروف بزرگ"""
        caps_text = "BITCOIN PRICE IS GOING TO THE MOON TODAY!!!"
        
        is_quality, issues = self.quality_filter.check_quality(caps_text)
        
        self.assertFalse(is_quality)
        self.assertTrue(any("حروف بزرگ" in issue for issue in issues))
    
    def test_character_repetition(self):
        """تست تکرار کاراکتر"""
        repeat_text = "Bitcoin priceeeee gooooooes up!!!!!!"
        
        is_quality, issues = self.quality_filter.check_quality(repeat_text)
        
        self.assertFalse(is_quality)
        self.assertTrue(any("تکرار" in issue for issue in issues))
    
    def test_empty_content(self):
        """تست محتوای خالی"""
        empty_text = ""
        
        is_quality, issues = self.quality_filter.check_quality(empty_text)
        
        self.assertFalse(is_quality)
        self.assertGreater(len(issues), 0)


class TestDuplicateFilterPerformance(unittest.TestCase):
    """تست‌های عملکرد فیلتر تکراری"""
    
    def setUp(self):
        self.filter = DuplicateContentFilter()
    
    def test_performance_large_history(self):
        """تست عملکرد با تاریخچه بزرگ"""
        import time
        
        # اضافه کردن تعداد زیادی محتوا
        for i in range(500):
            self.filter.add_content(f"Content number {i} about Bitcoin and cryptocurrency")
        
        # تست سرعت بررسی تکرار
        test_text = "New content about Bitcoin and cryptocurrency market"
        
        start_time = time.time()
        is_duplicate, similarity, reason = self.filter.is_duplicate(test_text)
        end_time = time.time()
        
        # باید کمتر از 1 ثانیه طول بکشد
        self.assertLess(end_time - start_time, 1.0)
    
    def test_memory_usage(self):
        """تست استفاده از حافظه"""
        import sys
        
        initial_size = sys.getsizeof(self.filter.content_fingerprints)
        
        # اضافه کردن محتوا
        for i in range(100):
            self.filter.add_content(f"Test content {i}")
        
        final_size = sys.getsizeof(self.filter.content_fingerprints)
        
        # حافظه نباید بیش از حد افزایش یابد
        size_increase = final_size - initial_size
        self.assertLess(size_increase, 1024 * 1024)  # کمتر از 1MB


if __name__ == '__main__':
    unittest.main()
