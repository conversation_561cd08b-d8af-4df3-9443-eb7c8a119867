#!/usr/bin/env python3
"""
اسکریپت اجرای تست‌ها
"""

import sys
import unittest
import time
from pathlib import Path

# اضافه کردن مسیر پروژه
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def run_all_tests():
    """اجرای همه تست‌ها"""
    print("🧪 شروع اجرای تست‌ها...")
    print("=" * 50)
    
    # پیدا کردن همه فایل‌های تست
    test_dir = project_root / "tests"
    test_files = list(test_dir.glob("test_*.py"))
    
    if not test_files:
        print("❌ هیچ فایل تستی یافت نشد")
        return False
    
    print(f"📁 یافت شد {len(test_files)} فایل تست:")
    for test_file in test_files:
        print(f"  - {test_file.name}")
    
    print("\n" + "=" * 50)
    
    # اجرای تست‌ها
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # بارگذاری تست‌ها
    for test_file in test_files:
        module_name = f"tests.{test_file.stem}"
        try:
            module = __import__(module_name, fromlist=[''])
            tests = loader.loadTestsFromModule(module)
            suite.addTests(tests)
            print(f"✅ بارگذاری {module_name}")
        except Exception as e:
            print(f"❌ خطا در بارگذاری {module_name}: {e}")
    
    # اجرای تست‌ها
    print("\n🚀 شروع اجرای تست‌ها...\n")
    
    start_time = time.time()
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    end_time = time.time()
    
    # نمایش نتایج
    print("\n" + "=" * 50)
    print("📊 نتایج تست:")
    print(f"⏱️  زمان اجرا: {end_time - start_time:.2f} ثانیه")
    print(f"✅ تست‌های موفق: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"❌ تست‌های ناموفق: {len(result.failures)}")
    print(f"💥 خطاها: {len(result.errors)}")
    
    if result.failures:
        print("\n🔴 تست‌های ناموفق:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    if result.errors:
        print("\n💥 خطاهای تست:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback.split('Exception:')[-1].strip()}")
    
    # نتیجه کلی
    success = len(result.failures) == 0 and len(result.errors) == 0
    
    if success:
        print("\n🎉 همه تست‌ها با موفقیت گذراندند!")
    else:
        print(f"\n⚠️  {len(result.failures) + len(result.errors)} تست ناموفق")
    
    return success


def run_specific_test(test_name):
    """اجرای تست خاص"""
    print(f"🧪 اجرای تست: {test_name}")
    
    try:
        # بارگذاری و اجرای تست خاص
        loader = unittest.TestLoader()
        suite = loader.loadTestsFromName(f"tests.{test_name}")
        
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)
        
        return len(result.failures) == 0 and len(result.errors) == 0
        
    except Exception as e:
        print(f"❌ خطا در اجرای تست {test_name}: {e}")
        return False


def run_performance_tests():
    """اجرای تست‌های عملکرد"""
    print("⚡ اجرای تست‌های عملکرد...")
    
    performance_tests = [
        "test_crypto_detector.TestCryptoDetectorPerformance",
        "test_duplicate_filter.TestDuplicateFilterPerformance",
        "test_database.TestDatabasePerformance"
    ]
    
    all_passed = True
    
    for test in performance_tests:
        print(f"\n🏃 اجرای {test}...")
        success = run_specific_test(test)
        if not success:
            all_passed = False
    
    return all_passed


def check_test_coverage():
    """بررسی پوشش تست‌ها"""
    print("📈 بررسی پوشش تست‌ها...")
    
    # لیست ماژول‌های اصلی که باید تست شوند
    main_modules = [
        "crypto_news_bot.content_filter.crypto_detector",
        "crypto_news_bot.content_filter.duplicate_filter",
        "crypto_news_bot.database.models",
        "crypto_news_bot.scraper.x_scraper",
        "crypto_news_bot.poster.x_poster",
        "crypto_news_bot.core.bot_manager"
    ]
    
    # بررسی وجود تست برای هر ماژول
    test_dir = project_root / "tests"
    existing_tests = [f.stem for f in test_dir.glob("test_*.py")]
    
    print("📋 وضعیت پوشش تست‌ها:")
    
    coverage_map = {
        "crypto_detector": "test_crypto_detector",
        "duplicate_filter": "test_duplicate_filter", 
        "models": "test_database",
        "x_scraper": "test_scraper",
        "x_poster": "test_poster",
        "bot_manager": "test_bot_manager"
    }
    
    for module_key, test_name in coverage_map.items():
        if test_name in existing_tests:
            print(f"  ✅ {module_key}: {test_name}")
        else:
            print(f"  ❌ {module_key}: تست موجود نیست")


def main():
    """تابع اصلی"""
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "all":
            success = run_all_tests()
        elif command == "performance":
            success = run_performance_tests()
        elif command == "coverage":
            check_test_coverage()
            return
        elif command.startswith("test_"):
            success = run_specific_test(command)
        else:
            print(f"❌ دستور نامعتبر: {command}")
            print("استفاده: python run_tests.py [all|performance|coverage|test_name]")
            return
    else:
        # اجرای همه تست‌ها به صورت پیش‌فرض
        success = run_all_tests()
    
    # خروج با کد مناسب
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
