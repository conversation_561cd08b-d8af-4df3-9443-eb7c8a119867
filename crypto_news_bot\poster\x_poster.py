"""
سیستم پست کردن در X (Twitter)
"""

import time
import random
from typing import Optional, Dict, List
from datetime import datetime
import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

from crypto_news_bot.config import X_USERNAME, X_PASSWORD, X_EMAIL, USER_AGENTS
from crypto_news_bot.utils.logger import poster_logger


class XPoster:
    """کلاس پست کردن در X"""
    
    def __init__(self):
        self.driver = None
        self.is_logged_in = False
        self.last_post_time = 0
        self.posts_today = 0
        self.max_posts_per_day = 50
        self.min_interval_between_posts = 300  # 5 دقیقه
        
    def setup_driver(self) -> bool:
        """راه‌اندازی Chrome driver"""
        try:
            options = uc.ChromeOptions()

            # تنظیمات ساده
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-gpu')

            # تنظیم User Agent
            options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')

            # حفظ session
            options.add_argument('--user-data-dir=./chrome_profile')

            try:
                self.driver = uc.Chrome(options=options, version_main=None)
            except Exception as e1:
                poster_logger.warning(f"تلاش اول ناموفق: {e1}")
                # تلاش دوم ساده‌تر
                options = uc.ChromeOptions()
                options.add_argument('--no-sandbox')
                self.driver = uc.Chrome(options=options)

            poster_logger.info("Chrome driver برای پست کردن راه‌اندازی شد")
            return True

        except Exception as e:
            poster_logger.error(f"خطا در راه‌اندازی driver: {e}")
            return False
    
    def close_driver(self):
        """بستن driver"""
        if self.driver:
            try:
                self.driver.quit()
                poster_logger.info("Driver پست کردن بسته شد")
            except Exception as e:
                poster_logger.error(f"خطا در بستن driver: {e}")
    
    def login(self, username: str = None, password: str = None, email: str = None) -> bool:
        """ورود به X"""
        try:
            if not self.driver:
                if not self.setup_driver():
                    return False
            
            username = username or X_USERNAME
            password = password or X_PASSWORD
            email = email or X_EMAIL
            
            if not username or not password:
                poster_logger.error("نام کاربری یا رمز عبور مشخص نشده")
                return False
            
            poster_logger.info("در حال ورود به X...")
            
            # رفتن به صفحه ورود
            self.driver.get("https://twitter.com/login")
            time.sleep(3)
            
            wait = WebDriverWait(self.driver, 20)
            
            # وارد کردن نام کاربری
            username_input = wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, 'input[autocomplete="username"]'))
            )
            username_input.clear()
            username_input.send_keys(username)
            username_input.send_keys(Keys.ENTER)
            
            time.sleep(2)
            
            # بررسی نیاز به ایمیل
            try:
                email_input = self.driver.find_element(By.CSS_SELECTOR, 'input[data-testid="ocfEnterTextTextInput"]')
                if email_input and email:
                    poster_logger.info("وارد کردن ایمیل...")
                    email_input.clear()
                    email_input.send_keys(email)
                    email_input.send_keys(Keys.ENTER)
                    time.sleep(2)
            except NoSuchElementException:
                pass
            
            # وارد کردن رمز عبور
            password_input = wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, 'input[autocomplete="current-password"]'))
            )
            password_input.clear()
            password_input.send_keys(password)
            password_input.send_keys(Keys.ENTER)
            
            time.sleep(3)
            
            # بررسی موفقیت ورود
            try:
                # اگر دکمه توییت کردن وجود داشته باشد، ورود موفق بوده
                wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, '[data-testid="SideNav_NewTweet_Button"]')))
                self.is_logged_in = True
                poster_logger.info("ورود به X موفقیت‌آمیز بود")
                return True
            except TimeoutException:
                poster_logger.error("ورود به X ناموفق")
                return False
                
        except Exception as e:
            poster_logger.error(f"خطا در ورود به X: {e}")
            return False
    
    def check_rate_limits(self) -> bool:
        """بررسی محدودیت‌های نرخ پست"""
        current_time = time.time()
        
        # بررسی فاصله زمانی بین پست‌ها
        if current_time - self.last_post_time < self.min_interval_between_posts:
            remaining_time = self.min_interval_between_posts - (current_time - self.last_post_time)
            poster_logger.warning(f"باید {remaining_time:.0f} ثانیه صبر کنید")
            return False
        
        # بررسی تعداد پست‌های روزانه
        if self.posts_today >= self.max_posts_per_day:
            poster_logger.warning("حد مجاز پست‌های روزانه رسیده")
            return False
        
        return True
    
    def create_tweet_text(self, news_data: Dict) -> str:
        """ایجاد متن توییت از داده خبر"""
        title = news_data.get('title', '')
        content = news_data.get('content', '')
        url = news_data.get('url', '')
        
        # انتخاب بهترین متن
        text = title if title else content
        
        # محدود کردن طول
        max_length = 240  # کمی کمتر از 280 برای URL
        if len(text) > max_length:
            text = text[:max_length-3] + "..."
        
        # اضافه کردن URL در صورت وجود
        if url:
            text += f"\n\n{url}"
        
        # اضافه کردن هشتگ‌های مرتبط
        hashtags = self._generate_hashtags(news_data)
        if hashtags:
            hashtag_text = " ".join(hashtags)
            if len(text) + len(hashtag_text) + 2 <= 280:
                text += f"\n\n{hashtag_text}"
        
        return text
    
    def _generate_hashtags(self, news_data: Dict) -> List[str]:
        """تولید هشتگ‌های مناسب"""
        hashtags = []
        text = (news_data.get('title', '') + ' ' + news_data.get('content', '')).lower()
        
        # هشتگ‌های پایه
        hashtags.append("#کریپتو")
        hashtags.append("#Crypto")
        
        # هشتگ‌های خاص
        if 'bitcoin' in text or 'btc' in text:
            hashtags.append("#Bitcoin")
        if 'ethereum' in text or 'eth' in text:
            hashtags.append("#Ethereum")
        if 'trading' in text:
            hashtags.append("#Trading")
        if 'defi' in text:
            hashtags.append("#DeFi")
        if 'nft' in text:
            hashtags.append("#NFT")
        
        return hashtags[:3]  # حداکثر 3 هشتگ
    
    def post_tweet(self, text: str) -> Dict:
        """پست کردن توییت"""
        result = {
            'success': False,
            'tweet_url': None,
            'error': None,
            'posted_at': datetime.now().isoformat()
        }
        
        try:
            if not self.is_logged_in:
                if not self.login():
                    result['error'] = "ورود ناموفق"
                    return result
            
            if not self.check_rate_limits():
                result['error'] = "محدودیت نرخ پست"
                return result
            
            poster_logger.info(f"در حال پست کردن: {text[:50]}...")
            
            # کلیک روی دکمه توییت کردن
            tweet_button = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, '[data-testid="SideNav_NewTweet_Button"]'))
            )
            tweet_button.click()
            
            time.sleep(2)
            
            # وارد کردن متن
            text_area = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, '[data-testid="tweetTextarea_0"]'))
            )
            
            # پاک کردن محتوای قبلی و وارد کردن متن جدید
            text_area.clear()
            text_area.send_keys(text)
            
            time.sleep(1)
            
            # کلیک روی دکمه ارسال
            post_button = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, '[data-testid="tweetButtonInline"]'))
            )
            post_button.click()
            
            # انتظار برای تایید ارسال
            time.sleep(3)
            
            # بررسی موفقیت
            try:
                # اگر modal بسته شده باشد، پست موفق بوده
                WebDriverWait(self.driver, 5).until_not(
                    EC.presence_of_element_located((By.CSS_SELECTOR, '[data-testid="tweetTextarea_0"]'))
                )
                
                result['success'] = True
                self.last_post_time = time.time()
                self.posts_today += 1
                
                poster_logger.info("توییت با موفقیت ارسال شد")
                
            except TimeoutException:
                result['error'] = "تایید ارسال دریافت نشد"
                
        except TimeoutException:
            result['error'] = "Timeout در پست کردن"
            poster_logger.error("Timeout در پست کردن توییت")
        except Exception as e:
            result['error'] = str(e)
            poster_logger.error(f"خطا در پست کردن توییت: {e}")
        
        return result
    
    def post_news(self, news_data: Dict) -> Dict:
        """پست کردن خبر"""
        try:
            # ایجاد متن توییت
            tweet_text = self.create_tweet_text(news_data)
            
            # پست کردن
            result = self.post_tweet(tweet_text)
            result['tweet_text'] = tweet_text
            result['news_data'] = news_data
            
            return result
            
        except Exception as e:
            poster_logger.error(f"خطا در پست کردن خبر: {e}")
            return {
                'success': False,
                'error': str(e),
                'tweet_text': None,
                'news_data': news_data
            }
    
    def get_account_status(self) -> Dict:
        """دریافت وضعیت حساب"""
        status = {
            'is_logged_in': self.is_logged_in,
            'posts_today': self.posts_today,
            'max_posts_per_day': self.max_posts_per_day,
            'last_post_time': self.last_post_time,
            'can_post_now': self.check_rate_limits()
        }
        
        return status
