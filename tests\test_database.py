"""
تست‌های پایگاه داده
"""

import unittest
import tempfile
import os
import sys
from pathlib import Path

# اضافه کردن مسیر پروژه
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from crypto_news_bot.database.models import DatabaseManager


class TestDatabaseManager(unittest.TestCase):
    """تست‌های مدیریت پایگاه داده"""
    
    def setUp(self):
        """راه‌اندازی تست با پایگاه داده موقت"""
        # ایجاد فایل موقت برای تست
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        
        self.db = DatabaseManager(Path(self.temp_db.name))
    
    def tearDown(self):
        """پاک کردن فایل موقت"""
        try:
            os.unlink(self.temp_db.name)
        except:
            pass
    
    def test_database_initialization(self):
        """تست مقداردهی اولیه پایگاه داده"""
        # بررسی وجود جداول
        with self.db.db_path.open() as f:
            # اگر فایل وجود داشته باشد، مقداردهی موفق بوده
            self.assertTrue(True)
    
    def test_add_news(self):
        """تست اضافه کردن خبر"""
        news_id = self.db.add_news(
            title="Test Bitcoin News",
            content="Bitcoin price reaches new high today",
            url="https://example.com/news/1",
            source="test_source",
            is_crypto=True,
            confidence=0.8
        )
        
        self.assertIsNotNone(news_id)
        self.assertIsInstance(news_id, int)
    
    def test_duplicate_news(self):
        """تست خبر تکراری"""
        # اضافه کردن خبر اول
        news_id1 = self.db.add_news(
            title="Test News",
            content="Same content for testing",
            url="https://example.com/1",
            source="test",
            is_crypto=True,
            confidence=0.7
        )
        
        # اضافه کردن خبر تکراری (محتوای یکسان)
        news_id2 = self.db.add_news(
            title="Different Title",
            content="Same content for testing",
            url="https://example.com/2",
            source="test",
            is_crypto=True,
            confidence=0.7
        )
        
        self.assertIsNotNone(news_id1)
        self.assertIsNone(news_id2)  # باید None برگردد چون تکراری است
    
    def test_get_unposted_crypto_news(self):
        """تست دریافت خبرهای کریپتو پست نشده"""
        # اضافه کردن چند خبر کریپتو
        for i in range(3):
            self.db.add_news(
                title=f"Crypto News {i}",
                content=f"Content about cryptocurrency {i}",
                url=f"https://example.com/{i}",
                source="test",
                is_crypto=True,
                confidence=0.5 + (i * 0.1)
            )
        
        # اضافه کردن خبر غیر کریپتو
        self.db.add_news(
            title="Non-crypto News",
            content="Regular news content",
            url="https://example.com/non-crypto",
            source="test",
            is_crypto=False,
            confidence=0.1
        )
        
        # دریافت خبرهای کریپتو
        crypto_news = self.db.get_unposted_crypto_news()
        
        self.assertEqual(len(crypto_news), 3)
        
        # بررسی مرتب‌سازی بر اساس امتیاز
        confidences = [news['confidence_score'] for news in crypto_news]
        self.assertEqual(confidences, sorted(confidences, reverse=True))
    
    def test_add_posted_tweet(self):
        """تست ثبت پست ارسال شده"""
        # اضافه کردن خبر
        news_id = self.db.add_news(
            title="Test News",
            content="Test content",
            url="https://example.com/test",
            source="test",
            is_crypto=True,
            confidence=0.8
        )
        
        # ثبت پست
        result = self.db.add_posted_tweet(
            news_id=news_id,
            tweet_text="Test tweet about crypto",
            tweet_url="https://twitter.com/test/123",
            status="success"
        )
        
        self.assertTrue(result)
        
        # بررسی اینکه خبر دیگر در لیست پست نشده‌ها نیست
        unposted = self.db.get_unposted_crypto_news()
        unposted_ids = [news['id'] for news in unposted]
        self.assertNotIn(news_id, unposted_ids)
    
    def test_news_sources(self):
        """تست مدیریت منابع خبری"""
        # اضافه کردن منبع
        result = self.db.add_news_source(
            name="Test Source",
            url="https://twitter.com/test_source",
            check_interval=300
        )
        self.assertTrue(result)
        
        # اضافه کردن منبع تکراری
        duplicate_result = self.db.add_news_source(
            name="Duplicate Source",
            url="https://twitter.com/test_source",  # URL تکراری
            check_interval=300
        )
        self.assertFalse(duplicate_result)
        
        # دریافت منابع فعال
        sources = self.db.get_active_news_sources()
        self.assertEqual(len(sources), 1)
        self.assertEqual(sources[0]['name'], "Test Source")
    
    def test_settings(self):
        """تست مدیریت تنظیمات"""
        # تنظیم مقدار
        result = self.db.set_setting("test_key", "test_value")
        self.assertTrue(result)
        
        # دریافت مقدار
        value = self.db.get_setting("test_key")
        self.assertEqual(value, "test_value")
        
        # دریافت مقدار پیش‌فرض برای کلید ناموجود
        default_value = self.db.get_setting("nonexistent_key", "default")
        self.assertEqual(default_value, "default")
        
        # به‌روزرسانی مقدار موجود
        self.db.set_setting("test_key", "updated_value")
        updated_value = self.db.get_setting("test_key")
        self.assertEqual(updated_value, "updated_value")
    
    def test_statistics(self):
        """تست مدیریت آمار"""
        from datetime import datetime
        
        today = datetime.now().strftime('%Y-%m-%d')
        
        # دریافت آمار اولیه
        stats = self.db.get_daily_statistics(today)
        self.assertEqual(stats['news_scraped'], 0)
        self.assertEqual(stats['crypto_news_found'], 0)
        self.assertEqual(stats['tweets_posted'], 0)
        
        # به‌روزرسانی آمار
        self.db.update_statistics('news_scraped', 5, today)
        self.db.update_statistics('crypto_news_found', 2, today)
        self.db.update_statistics('tweets_posted', 1, today)
        
        # بررسی آمار به‌روز شده
        updated_stats = self.db.get_daily_statistics(today)
        self.assertEqual(updated_stats['news_scraped'], 5)
        self.assertEqual(updated_stats['crypto_news_found'], 2)
        self.assertEqual(updated_stats['tweets_posted'], 1)
        
        # به‌روزرسانی اضافی
        self.db.update_statistics('news_scraped', 3, today)
        final_stats = self.db.get_daily_statistics(today)
        self.assertEqual(final_stats['news_scraped'], 8)  # 5 + 3
    
    def test_update_source_last_checked(self):
        """تست به‌روزرسانی زمان آخرین بررسی منبع"""
        # اضافه کردن منبع
        self.db.add_news_source("Test Source", "https://twitter.com/test")
        
        sources = self.db.get_active_news_sources()
        source_id = sources[0]['id']
        
        # به‌روزرسانی زمان بررسی
        result = self.db.update_source_last_checked(source_id)
        self.assertTrue(result)
        
        # بررسی به‌روزرسانی
        updated_sources = self.db.get_active_news_sources()
        self.assertIsNotNone(updated_sources[0]['last_checked'])


class TestDatabasePerformance(unittest.TestCase):
    """تست‌های عملکرد پایگاه داده"""
    
    def setUp(self):
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.db = DatabaseManager(Path(self.temp_db.name))
    
    def tearDown(self):
        try:
            os.unlink(self.temp_db.name)
        except:
            pass
    
    def test_bulk_insert_performance(self):
        """تست عملکرد درج انبوه"""
        import time
        
        start_time = time.time()
        
        # درج 100 خبر
        for i in range(100):
            self.db.add_news(
                title=f"News {i}",
                content=f"Content for news {i} about cryptocurrency",
                url=f"https://example.com/{i}",
                source="test",
                is_crypto=True,
                confidence=0.5
            )
        
        end_time = time.time()
        
        # باید کمتر از 5 ثانیه طول بکشد
        self.assertLess(end_time - start_time, 5.0)
    
    def test_query_performance(self):
        """تست عملکرد کوئری"""
        import time
        
        # اضافه کردن داده‌های تست
        for i in range(50):
            news_id = self.db.add_news(
                title=f"News {i}",
                content=f"Content {i}",
                url=f"https://example.com/{i}",
                source="test",
                is_crypto=True,
                confidence=0.5
            )
            
            # پست کردن نیمی از خبرها
            if i % 2 == 0:
                self.db.add_posted_tweet(news_id, f"Tweet {i}")
        
        # تست سرعت کوئری
        start_time = time.time()
        
        for _ in range(10):
            unposted = self.db.get_unposted_crypto_news()
        
        end_time = time.time()
        
        # باید کمتر از 1 ثانیه طول بکشد
        self.assertLess(end_time - start_time, 1.0)


if __name__ == '__main__':
    unittest.main()
