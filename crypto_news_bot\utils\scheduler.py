"""
سیستم زمان‌بندی و مانیتورینگ
"""

import time
import threading
from datetime import datetime, timedelta
from typing import Callable, Dict, List, Optional
from dataclasses import dataclass
from enum import Enum

from crypto_news_bot.utils.logger import main_logger


class TaskStatus(Enum):
    """وضعیت تسک"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class ScheduledTask:
    """کلاس تسک زمان‌بندی شده"""
    name: str
    func: Callable
    interval: int  # ثانیه
    next_run: datetime
    last_run: Optional[datetime] = None
    status: TaskStatus = TaskStatus.PENDING
    error_count: int = 0
    max_errors: int = 5
    enabled: bool = True
    
    def should_run(self) -> bool:
        """بررسی اینکه آیا تسک باید اجرا شود"""
        return (
            self.enabled and 
            self.status != TaskStatus.RUNNING and
            self.error_count < self.max_errors and
            datetime.now() >= self.next_run
        )
    
    def update_next_run(self):
        """به‌روزرسانی زمان اجرای بعدی"""
        self.next_run = datetime.now() + timedelta(seconds=self.interval)
    
    def mark_completed(self):
        """علامت‌گذاری تسک به عنوان تکمیل شده"""
        self.status = TaskStatus.COMPLETED
        self.last_run = datetime.now()
        self.update_next_run()
        self.error_count = 0  # ریست کردن شمارنده خطا
    
    def mark_failed(self, error: Exception):
        """علامت‌گذاری تسک به عنوان ناموفق"""
        self.status = TaskStatus.FAILED
        self.error_count += 1
        self.update_next_run()
        
        main_logger.error(f"تسک {self.name} ناموفق: {error}")
        
        if self.error_count >= self.max_errors:
            self.enabled = False
            main_logger.error(f"تسک {self.name} غیرفعال شد (خطاهای زیاد)")


class TaskScheduler:
    """زمان‌بند تسک‌ها"""
    
    def __init__(self):
        self.tasks: Dict[str, ScheduledTask] = {}
        self.is_running = False
        self.scheduler_thread: Optional[threading.Thread] = None
        self.stop_event = threading.Event()
        
        # آمار
        self.stats = {
            'total_runs': 0,
            'successful_runs': 0,
            'failed_runs': 0,
            'start_time': None
        }
    
    def add_task(self, name: str, func: Callable, interval: int, 
                 immediate: bool = False) -> bool:
        """اضافه کردن تسک جدید"""
        try:
            if name in self.tasks:
                main_logger.warning(f"تسک {name} قبلاً وجود دارد")
                return False
            
            next_run = datetime.now() if immediate else datetime.now() + timedelta(seconds=interval)
            
            task = ScheduledTask(
                name=name,
                func=func,
                interval=interval,
                next_run=next_run
            )
            
            self.tasks[name] = task
            main_logger.info(f"تسک {name} اضافه شد (فاصله: {interval} ثانیه)")
            return True
            
        except Exception as e:
            main_logger.error(f"خطا در اضافه کردن تسک {name}: {e}")
            return False
    
    def remove_task(self, name: str) -> bool:
        """حذف تسک"""
        if name in self.tasks:
            del self.tasks[name]
            main_logger.info(f"تسک {name} حذف شد")
            return True
        return False
    
    def enable_task(self, name: str) -> bool:
        """فعال کردن تسک"""
        if name in self.tasks:
            self.tasks[name].enabled = True
            self.tasks[name].error_count = 0
            main_logger.info(f"تسک {name} فعال شد")
            return True
        return False
    
    def disable_task(self, name: str) -> bool:
        """غیرفعال کردن تسک"""
        if name in self.tasks:
            self.tasks[name].enabled = False
            main_logger.info(f"تسک {name} غیرفعال شد")
            return True
        return False
    
    def start(self):
        """شروع زمان‌بند"""
        if self.is_running:
            main_logger.warning("زمان‌بند در حال اجرا است")
            return
        
        self.is_running = True
        self.stop_event.clear()
        self.stats['start_time'] = datetime.now()
        
        self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self.scheduler_thread.start()
        
        main_logger.info("زمان‌بند شروع شد")
    
    def stop(self):
        """توقف زمان‌بند"""
        if not self.is_running:
            return
        
        self.is_running = False
        self.stop_event.set()
        
        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=5)
        
        main_logger.info("زمان‌بند متوقف شد")
    
    def _scheduler_loop(self):
        """حلقه اصلی زمان‌بند"""
        while self.is_running and not self.stop_event.is_set():
            try:
                current_time = datetime.now()
                
                # بررسی تسک‌هایی که باید اجرا شوند
                for task in self.tasks.values():
                    if task.should_run():
                        self._execute_task(task)
                
                # انتظار کوتاه
                self.stop_event.wait(1)  # بررسی هر ثانیه
                
            except Exception as e:
                main_logger.error(f"خطا در حلقه زمان‌بند: {e}")
                time.sleep(5)
    
    def _execute_task(self, task: ScheduledTask):
        """اجرای یک تسک"""
        try:
            task.status = TaskStatus.RUNNING
            main_logger.debug(f"شروع اجرای تسک {task.name}")
            
            # اجرای تسک در thread جداگانه
            task_thread = threading.Thread(
                target=self._run_task_safely,
                args=(task,),
                daemon=True
            )
            task_thread.start()
            
        except Exception as e:
            task.mark_failed(e)
            self.stats['failed_runs'] += 1
    
    def _run_task_safely(self, task: ScheduledTask):
        """اجرای ایمن تسک"""
        try:
            # اجرای تابع تسک
            task.func()
            
            # علامت‌گذاری موفقیت
            task.mark_completed()
            self.stats['successful_runs'] += 1
            self.stats['total_runs'] += 1
            
            main_logger.debug(f"تسک {task.name} با موفقیت تکمیل شد")
            
        except Exception as e:
            task.mark_failed(e)
            self.stats['failed_runs'] += 1
            self.stats['total_runs'] += 1
    
    def get_task_status(self, name: str) -> Optional[Dict]:
        """دریافت وضعیت تسک"""
        if name not in self.tasks:
            return None
        
        task = self.tasks[name]
        return {
            'name': task.name,
            'status': task.status.value,
            'enabled': task.enabled,
            'last_run': task.last_run.isoformat() if task.last_run else None,
            'next_run': task.next_run.isoformat(),
            'error_count': task.error_count,
            'interval': task.interval
        }
    
    def get_all_tasks_status(self) -> List[Dict]:
        """دریافت وضعیت همه تسک‌ها"""
        return [self.get_task_status(name) for name in self.tasks.keys()]
    
    def get_scheduler_stats(self) -> Dict:
        """دریافت آمار زمان‌بند"""
        uptime = None
        if self.stats['start_time']:
            uptime = (datetime.now() - self.stats['start_time']).total_seconds()
        
        return {
            'is_running': self.is_running,
            'total_tasks': len(self.tasks),
            'enabled_tasks': sum(1 for task in self.tasks.values() if task.enabled),
            'uptime_seconds': uptime,
            'total_runs': self.stats['total_runs'],
            'successful_runs': self.stats['successful_runs'],
            'failed_runs': self.stats['failed_runs'],
            'success_rate': (
                self.stats['successful_runs'] / max(self.stats['total_runs'], 1) * 100
            )
        }


class PerformanceMonitor:
    """مانیتور عملکرد"""
    
    def __init__(self, max_history: int = 1000):
        self.max_history = max_history
        self.metrics_history: List[Dict] = []
        self.alerts: List[Dict] = []
        self.thresholds = {
            'error_rate': 10.0,  # درصد
            'response_time': 30.0,  # ثانیه
            'memory_usage': 80.0,  # درصد
        }
    
    def record_metric(self, metric_name: str, value: float, 
                     timestamp: datetime = None):
        """ثبت متریک"""
        if timestamp is None:
            timestamp = datetime.now()
        
        metric = {
            'name': metric_name,
            'value': value,
            'timestamp': timestamp
        }
        
        self.metrics_history.append(metric)
        
        # محدود کردن تاریخچه
        if len(self.metrics_history) > self.max_history:
            self.metrics_history.pop(0)
        
        # بررسی آستانه‌ها
        self._check_thresholds(metric_name, value)
    
    def _check_thresholds(self, metric_name: str, value: float):
        """بررسی آستانه‌های هشدار"""
        if metric_name in self.thresholds:
            threshold = self.thresholds[metric_name]
            
            if value > threshold:
                alert = {
                    'metric': metric_name,
                    'value': value,
                    'threshold': threshold,
                    'timestamp': datetime.now(),
                    'message': f"{metric_name} از آستانه {threshold} گذشت: {value}"
                }
                
                self.alerts.append(alert)
                main_logger.warning(alert['message'])
                
                # محدود کردن تعداد هشدارها
                if len(self.alerts) > 100:
                    self.alerts.pop(0)
    
    def get_recent_metrics(self, metric_name: str, 
                          minutes: int = 60) -> List[Dict]:
        """دریافت متریک‌های اخیر"""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        
        return [
            metric for metric in self.metrics_history
            if metric['name'] == metric_name and metric['timestamp'] > cutoff_time
        ]
    
    def get_average_metric(self, metric_name: str, 
                          minutes: int = 60) -> Optional[float]:
        """محاسبه میانگین متریک"""
        recent_metrics = self.get_recent_metrics(metric_name, minutes)
        
        if not recent_metrics:
            return None
        
        return sum(m['value'] for m in recent_metrics) / len(recent_metrics)
    
    def get_alerts(self, minutes: int = 60) -> List[Dict]:
        """دریافت هشدارهای اخیر"""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        
        return [
            alert for alert in self.alerts
            if alert['timestamp'] > cutoff_time
        ]
    
    def clear_old_data(self, hours: int = 24):
        """پاک کردن داده‌های قدیمی"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        # پاک کردن متریک‌های قدیمی
        self.metrics_history = [
            metric for metric in self.metrics_history
            if metric['timestamp'] > cutoff_time
        ]
        
        # پاک کردن هشدارهای قدیمی
        self.alerts = [
            alert for alert in self.alerts
            if alert['timestamp'] > cutoff_time
        ]
