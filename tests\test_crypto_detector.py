"""
تست‌های سیستم تشخیص کریپتو
"""

import unittest
import sys
from pathlib import Path

# اضافه کردن مسیر پروژه
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from crypto_news_bot.content_filter.crypto_detector import CryptoContentDetector


class TestCryptoDetector(unittest.TestCase):
    """تست‌های تشخیص محتوای کریپتو"""
    
    def setUp(self):
        """راه‌اندازی تست"""
        self.detector = CryptoContentDetector()
    
    def test_bitcoin_detection(self):
        """تست تشخیص خبرهای بیت کوین"""
        # متن‌های مثبت
        positive_texts = [
            "Bitcoin price reaches new all-time high of $50,000",
            "BTC trading volume increases by 25% today",
            "Major company adopts Bitcoin as payment method",
            "بیت کوین به رکورد جدید رسید",
            "قیمت BTC امروز 10% افزایش یافت"
        ]
        
        for text in positive_texts:
            is_crypto, confidence = self.detector.calculate_crypto_score(text)
            self.assertTrue(is_crypto, f"Failed to detect crypto in: {text}")
            self.assertGreater(confidence, 0.3, f"Low confidence for: {text}")
    
    def test_ethereum_detection(self):
        """تست تشخیص خبرهای اتریوم"""
        positive_texts = [
            "Ethereum 2.0 upgrade completed successfully",
            "ETH smart contracts reach milestone",
            "DeFi protocols on Ethereum grow rapidly",
            "اتریوم به سطح جدیدی رسید"
        ]
        
        for text in positive_texts:
            is_crypto, confidence = self.detector.calculate_crypto_score(text)
            self.assertTrue(is_crypto, f"Failed to detect crypto in: {text}")
    
    def test_general_crypto_detection(self):
        """تست تشخیص خبرهای کریپتو عمومی"""
        positive_texts = [
            "Cryptocurrency market cap exceeds $2 trillion",
            "New blockchain technology revolutionizes finance",
            "DeFi yields attract institutional investors",
            "NFT marketplace sees record sales",
            "Altcoin season begins with massive gains"
        ]
        
        for text in positive_texts:
            is_crypto, confidence = self.detector.calculate_crypto_score(text)
            self.assertTrue(is_crypto, f"Failed to detect crypto in: {text}")
    
    def test_negative_detection(self):
        """تست عدم تشخیص اشتباه"""
        negative_texts = [
            "Weather forecast shows sunny skies ahead",
            "New restaurant opens downtown",
            "Sports team wins championship game",
            "Movie review: latest blockbuster disappoints",
            "Travel tips for summer vacation"
        ]
        
        for text in negative_texts:
            is_crypto, confidence = self.detector.calculate_crypto_score(text)
            self.assertFalse(is_crypto, f"False positive for: {text}")
    
    def test_price_mentions(self):
        """تست تشخیص ذکر قیمت"""
        price_texts = [
            "Bitcoin trades at $45,000 with 5% gain",
            "ETH price drops to $3,200 after news",
            "Crypto market loses $100 billion in value"
        ]
        
        for text in price_texts:
            features = self.detector.extract_crypto_features(text)
            self.assertGreater(features['price_mentions'], 0, 
                             f"Failed to detect price in: {text}")
    
    def test_percentage_mentions(self):
        """تست تشخیص درصدها"""
        percentage_texts = [
            "Bitcoin up 15% in last 24 hours",
            "Crypto portfolio gains 25% this week",
            "Market correction: -10% across all coins"
        ]
        
        for text in percentage_texts:
            features = self.detector.extract_crypto_features(text)
            self.assertGreater(features['percentage_mentions'], 0,
                             f"Failed to detect percentage in: {text}")
    
    def test_hashtag_detection(self):
        """تست تشخیص هشتگ‌های کریپتو"""
        hashtag_texts = [
            "Great news for #Bitcoin holders today!",
            "Check out this #DeFi protocol #crypto",
            "#Ethereum network upgrade successful"
        ]
        
        for text in hashtag_texts:
            features = self.detector.extract_crypto_features(text)
            self.assertGreater(features['hashtag_score'], 0,
                             f"Failed to detect crypto hashtags in: {text}")
    
    def test_content_quality(self):
        """تست تحلیل کیفیت محتوا"""
        # محتوای با کیفیت
        quality_text = "Bitcoin's latest upgrade improves transaction speed and reduces fees significantly."
        quality_metrics = self.detector.analyze_content_quality(quality_text)
        
        self.assertGreater(quality_metrics['readability'], 0.3)
        self.assertGreater(quality_metrics['informativeness'], 0.3)
        self.assertLess(quality_metrics['spam_likelihood'], 0.5)
        
        # محتوای اسپم
        spam_text = "BUY NOW!!! URGENT!!! BITCOIN TO THE MOON!!! CLICK HERE!!!"
        spam_metrics = self.detector.analyze_content_quality(spam_text)
        
        self.assertGreater(spam_metrics['spam_likelihood'], 0.5)
    
    def test_mixed_content(self):
        """تست محتوای ترکیبی"""
        mixed_texts = [
            "Today's weather is nice, but Bitcoin price is even better at $50k",
            "Had lunch at new restaurant, then checked my crypto portfolio",
            "Sports news: team wins, crypto news: Bitcoin reaches ATH"
        ]
        
        for text in mixed_texts:
            is_crypto, confidence = self.detector.calculate_crypto_score(text)
            # باید کریپتو تشخیص داده شود اما با اعتماد متوسط
            self.assertTrue(is_crypto, f"Failed to detect crypto in mixed content: {text}")
            self.assertLess(confidence, 0.8, f"Too high confidence for mixed content: {text}")
    
    def test_edge_cases(self):
        """تست موارد خاص"""
        edge_cases = [
            "",  # متن خالی
            "a",  # متن خیلی کوتاه
            "crypto " * 100,  # تکرار زیاد
            "Bitcoin Bitcoin Bitcoin Bitcoin Bitcoin"  # تکرار کلمه کلیدی
        ]
        
        for text in edge_cases:
            try:
                is_crypto, confidence = self.detector.calculate_crypto_score(text)
                # نباید خطا بدهد
                self.assertIsInstance(is_crypto, bool)
                self.assertIsInstance(confidence, float)
                self.assertGreaterEqual(confidence, 0.0)
                self.assertLessEqual(confidence, 1.0)
            except Exception as e:
                self.fail(f"Exception raised for edge case '{text}': {e}")


class TestCryptoDetectorPerformance(unittest.TestCase):
    """تست‌های عملکرد"""
    
    def setUp(self):
        self.detector = CryptoContentDetector()
    
    def test_performance_large_text(self):
        """تست عملکرد با متن بزرگ"""
        import time
        
        # متن بزرگ
        large_text = "Bitcoin is a cryptocurrency. " * 1000
        
        start_time = time.time()
        is_crypto, confidence = self.detector.calculate_crypto_score(large_text)
        end_time = time.time()
        
        # باید کمتر از 5 ثانیه طول بکشد
        self.assertLess(end_time - start_time, 5.0)
        self.assertTrue(is_crypto)
    
    def test_performance_multiple_texts(self):
        """تست عملکرد با متن‌های متعدد"""
        import time
        
        texts = [
            "Bitcoin price analysis for today",
            "Ethereum smart contract development",
            "DeFi protocol launches new feature",
            "Cryptocurrency market trends",
            "Blockchain technology advancement"
        ] * 20  # 100 متن
        
        start_time = time.time()
        for text in texts:
            self.detector.calculate_crypto_score(text)
        end_time = time.time()
        
        # باید کمتر از 10 ثانیه طول بکشد
        self.assertLess(end_time - start_time, 10.0)


if __name__ == '__main__':
    unittest.main()
