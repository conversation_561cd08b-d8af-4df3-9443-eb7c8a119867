"""
پنجره آمار تفصیلی
"""

import tkinter as tk
from tkinter import ttk
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from datetime import datetime, timedelta
from crypto_news_bot.database.models import DatabaseManager


class StatsWindow:
    """پنجره آمار تفصیلی"""
    
    def __init__(self, parent):
        self.parent = parent
        self.db = DatabaseManager()
        
        self.window = tk.Toplevel(parent)
        self.window.title("آمار تفصیلی")
        self.window.geometry("800x600")
        self.window.transient(parent)
        
        self.setup_ui()
        self.load_stats()
    
    def setup_ui(self):
        """راه‌اندازی رابط کاربری"""
        # نوار ابزار
        toolbar = ttk.Frame(self.window)
        toolbar.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(toolbar, text="به‌روزرسانی", command=self.load_stats).pack(side=tk.LEFT)
        ttk.Button(toolbar, text="صادرات", command=self.export_stats).pack(side=tk.LEFT, padx=5)
        
        # بازه زمانی
        ttk.Label(toolbar, text="بازه:").pack(side=tk.LEFT, padx=(20, 5))
        self.period_var = tk.StringVar(value="7 روز گذشته")
        period_combo = ttk.Combobox(toolbar, textvariable=self.period_var, width=15,
                                   values=["7 روز گذشته", "30 روز گذشته", "3 ماه گذشته"])
        period_combo.pack(side=tk.LEFT)
        period_combo.bind("<<ComboboxSelected>>", lambda e: self.load_stats())
        
        # notebook برای نمودارها
        self.notebook = ttk.Notebook(self.window)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # تب نمودار کلی
        self.create_overview_tab()
        
        # تب نمودار عملکرد
        self.create_performance_tab()
        
        # تب جدول تفصیلی
        self.create_detailed_tab()
    
    def create_overview_tab(self):
        """ایجاد تب نمای کلی"""
        overview_frame = ttk.Frame(self.notebook)
        self.notebook.add(overview_frame, text="نمای کلی")
        
        # نمودار میله‌ای
        self.overview_fig, self.overview_ax = plt.subplots(figsize=(10, 6))
        self.overview_canvas = FigureCanvasTkAgg(self.overview_fig, overview_frame)
        self.overview_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    
    def create_performance_tab(self):
        """ایجاد تب عملکرد"""
        performance_frame = ttk.Frame(self.notebook)
        self.notebook.add(performance_frame, text="عملکرد")
        
        # نمودار خطی
        self.performance_fig, self.performance_ax = plt.subplots(figsize=(10, 6))
        self.performance_canvas = FigureCanvasTkAgg(self.performance_fig, performance_frame)
        self.performance_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    
    def create_detailed_tab(self):
        """ایجاد تب جدول تفصیلی"""
        detailed_frame = ttk.Frame(self.notebook)
        self.notebook.add(detailed_frame, text="جزئیات")
        
        # جدول تفصیلی
        columns = ("تاریخ", "خبرهای جمع‌آوری شده", "خبرهای کریپتو", "پست‌ها", "نرخ موفقیت")
        self.detailed_tree = ttk.Treeview(detailed_frame, columns=columns, show="headings", height=15)
        
        for col in columns:
            self.detailed_tree.heading(col, text=col)
            self.detailed_tree.column(col, width=150)
        
        # اسکرول بار
        detailed_scrollbar = ttk.Scrollbar(detailed_frame, orient=tk.VERTICAL, command=self.detailed_tree.yview)
        self.detailed_tree.configure(yscrollcommand=detailed_scrollbar.set)
        
        self.detailed_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        detailed_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def load_stats(self):
        """بارگذاری آمار"""
        try:
            # تعیین بازه زمانی
            period = self.period_var.get()
            if "7 روز" in period:
                days = 7
            elif "30 روز" in period:
                days = 30
            else:
                days = 90
            
            # دریافت داده‌ها
            stats_data = self.get_stats_data(days)
            
            # به‌روزرسانی نمودارها
            self.update_overview_chart(stats_data)
            self.update_performance_chart(stats_data)
            self.update_detailed_table(stats_data)
            
        except Exception as e:
            print(f"خطا در بارگذاری آمار: {e}")
    
    def get_stats_data(self, days: int):
        """دریافت داده‌های آمار"""
        # این قسمت باید با دیتابیس واقعی پیاده‌سازی شود
        # در اینجا داده‌های نمونه استفاده می‌شود
        
        dates = []
        news_scraped = []
        crypto_news = []
        posts = []
        
        for i in range(days):
            date = datetime.now() - timedelta(days=i)
            dates.append(date.strftime("%Y-%m-%d"))
            
            # داده‌های نمونه
            import random
            news_scraped.append(random.randint(10, 50))
            crypto_news.append(random.randint(2, 15))
            posts.append(random.randint(1, 8))
        
        return {
            'dates': dates[::-1],  # معکوس کردن برای ترتیب صحیح
            'news_scraped': news_scraped[::-1],
            'crypto_news': crypto_news[::-1],
            'posts': posts[::-1]
        }
    
    def update_overview_chart(self, data):
        """به‌روزرسانی نمودار کلی"""
        self.overview_ax.clear()
        
        x = range(len(data['dates']))
        width = 0.25
        
        self.overview_ax.bar([i - width for i in x], data['news_scraped'], 
                           width, label='خبرهای جمع‌آوری شده', alpha=0.8)
        self.overview_ax.bar(x, data['crypto_news'], 
                           width, label='خبرهای کریپتو', alpha=0.8)
        self.overview_ax.bar([i + width for i in x], data['posts'], 
                           width, label='پست‌ها', alpha=0.8)
        
        self.overview_ax.set_xlabel('تاریخ')
        self.overview_ax.set_ylabel('تعداد')
        self.overview_ax.set_title('آمار کلی عملکرد ربات')
        self.overview_ax.legend()
        
        # تنظیم برچسب‌های محور x
        step = max(1, len(data['dates']) // 10)
        self.overview_ax.set_xticks(range(0, len(data['dates']), step))
        self.overview_ax.set_xticklabels([data['dates'][i] for i in range(0, len(data['dates']), step)], 
                                       rotation=45)
        
        self.overview_fig.tight_layout()
        self.overview_canvas.draw()
    
    def update_performance_chart(self, data):
        """به‌روزرسانی نمودار عملکرد"""
        self.performance_ax.clear()
        
        # محاسبه نرخ موفقیت
        success_rate = []
        for i in range(len(data['dates'])):
            if data['crypto_news'][i] > 0:
                rate = (data['posts'][i] / data['crypto_news'][i]) * 100
            else:
                rate = 0
            success_rate.append(min(100, rate))
        
        self.performance_ax.plot(data['dates'], success_rate, 'o-', linewidth=2, markersize=6)
        self.performance_ax.set_xlabel('تاریخ')
        self.performance_ax.set_ylabel('نرخ موفقیت (%)')
        self.performance_ax.set_title('نرخ موفقیت پست کردن')
        self.performance_ax.grid(True, alpha=0.3)
        
        # تنظیم برچسب‌های محور x
        step = max(1, len(data['dates']) // 10)
        self.performance_ax.set_xticks(range(0, len(data['dates']), step))
        self.performance_ax.set_xticklabels([data['dates'][i] for i in range(0, len(data['dates']), step)], 
                                          rotation=45)
        
        self.performance_fig.tight_layout()
        self.performance_canvas.draw()
    
    def update_detailed_table(self, data):
        """به‌روزرسانی جدول تفصیلی"""
        # پاک کردن داده‌های قبلی
        for item in self.detailed_tree.get_children():
            self.detailed_tree.delete(item)
        
        # اضافه کردن داده‌های جدید
        for i in range(len(data['dates'])):
            if data['crypto_news'][i] > 0:
                success_rate = f"{(data['posts'][i] / data['crypto_news'][i]) * 100:.1f}%"
            else:
                success_rate = "0%"
            
            self.detailed_tree.insert('', tk.END, values=(
                data['dates'][i],
                data['news_scraped'][i],
                data['crypto_news'][i],
                data['posts'][i],
                success_rate
            ))
    
    def export_stats(self):
        """صادرات آمار"""
        from tkinter import filedialog
        import csv
        
        filename = filedialog.asksaveasfilename(
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)
                    
                    # نوشتن هدر
                    writer.writerow(["تاریخ", "خبرهای جمع‌آوری شده", "خبرهای کریپتو", "پست‌ها", "نرخ موفقیت"])
                    
                    # نوشتن داده‌ها
                    for item in self.detailed_tree.get_children():
                        values = self.detailed_tree.item(item)['values']
                        writer.writerow(values)
                
                tk.messagebox.showinfo("موفقیت", f"آمار در {filename} ذخیره شد")
                
            except Exception as e:
                tk.messagebox.showerror("خطا", f"خطا در صادرات: {e}")
