"""
مدیریت اصلی ربات
"""

import time
import threading
from datetime import datetime
from typing import List, Dict, Optional
from concurrent.futures import ThreadPoolExecutor

from crypto_news_bot.database.models import DatabaseManager
from crypto_news_bot.scraper.x_scraper import XS<PERSON>raper
from crypto_news_bot.poster.x_poster import XPoster
from crypto_news_bot.poster.x_http_poster import XHttpPoster
from crypto_news_bot.content_filter.crypto_detector import CryptoContentDetector
from crypto_news_bot.content_filter.duplicate_filter import DuplicateContentFilter
from crypto_news_bot.utils.logger import main_logger
from crypto_news_bot.config import SCRAPING_INTERVAL, DEFAULT_NEWS_SOURCES


class BotManager:
    """کلاس مدیریت اصلی ربات"""
    
    def __init__(self):
        self.db = DatabaseManager()
        self.scraper = XScraper()
        self.poster = XHttpPoster()  # استفاده از HTTP poster
        self.crypto_detector = CryptoContentDetector()
        self.duplicate_filter = DuplicateContentFilter()
        
        self.is_running = False
        self.is_paused = False
        self.worker_thread = None
        self.executor = ThreadPoolExecutor(max_workers=3)
        
        # آمار
        self.stats = {
            'news_scraped_today': 0,
            'crypto_news_found_today': 0,
            'posts_made_today': 0,
            'errors_today': 0,
            'last_check_time': None,
            'current_task': None,
            'progress': 0,
            'next_cycle_in': 0
        }
        
        # تنظیمات
        self.settings = {
            'check_interval': SCRAPING_INTERVAL * 60,  # تبدیل به ثانیه
            'max_posts_per_day': 50,
            'min_crypto_score': 0.3,
            'post_interval': 300  # 5 دقیقه
        }
        
        self.load_settings()
        self.initialize_sources()
    
    def load_settings(self):
        """بارگذاری تنظیمات از دیتابیس"""
        try:
            self.settings['check_interval'] = int(self.db.get_setting('check_interval', '5')) * 60
            self.settings['max_posts_per_day'] = int(self.db.get_setting('max_posts_daily', '50'))
            self.settings['min_crypto_score'] = float(self.db.get_setting('min_crypto_score', '0.3'))
            self.settings['post_interval'] = int(self.db.get_setting('post_interval', '5')) * 60

            main_logger.info(f"تنظیمات بارگذاری شد: فاصله بررسی {self.settings['check_interval']//60} دقیقه، حداکثر پست {self.settings['max_posts_per_day']}")

        except Exception as e:
            main_logger.error(f"خطا در بارگذاری تنظیمات: {e}")

    def reload_settings(self):
        """بارگذاری مجدد تنظیمات"""
        self.load_settings()

        # بارگذاری مجدد تنظیمات در اجزای دیگر
        if hasattr(self, 'crypto_detector'):
            self.crypto_detector.load_settings()

        main_logger.info("تنظیمات مجدداً بارگذاری شد")

    def get_x_credentials(self):
        """دریافت اطلاعات حساب X"""
        try:
            username = self.db.get_setting("x_username", "")
            email = self.db.get_setting("x_email", "")

            # دریافت رمز عبور رمزگشایی شده
            encrypted_password = self.db.get_setting("x_password", "")
            password = ""

            if encrypted_password:
                try:
                    import base64
                    password = base64.b64decode(encrypted_password.encode()).decode()
                except:
                    password = ""

            return {
                'username': username,
                'email': email,
                'password': password
            }

        except Exception as e:
            main_logger.error(f"خطا در دریافت اطلاعات حساب X: {e}")
            return {'username': '', 'email': '', 'password': ''}
    
    def initialize_sources(self):
        """مقداردهی اولیه منابع خبری"""
        try:
            existing_sources = self.db.get_active_news_sources()
            
            if not existing_sources:
                # اضافه کردن منابع پیش‌فرض
                for source_url in DEFAULT_NEWS_SOURCES:
                    source_name = source_url.split('/')[-1]
                    self.db.add_news_source(source_name, source_url)
                
                main_logger.info("منابع پیش‌فرض اضافه شدند")
            
        except Exception as e:
            main_logger.error(f"خطا در مقداردهی منابع: {e}")
    
    def start(self):
        """شروع ربات"""
        if self.is_running:
            main_logger.warning("ربات در حال اجرا است")
            return
        
        try:
            self.is_running = True
            self.is_paused = False

            main_logger.info("ربات شروع شد")

            # ورود به X
            if not self._login_to_x():
                main_logger.error("ورود به X ناموفق - ربات در حالت محدود کار می‌کند")

            # شروع حلقه اصلی در thread جداگانه
            self.worker_thread = threading.Thread(target=self._main_loop, daemon=True)
            self.worker_thread.start()

        except Exception as e:
            main_logger.error(f"خطا در شروع ربات: {e}")
            self.is_running = False

    def _login_to_x(self) -> bool:
        """ورود به X"""
        try:
            credentials = self.get_x_credentials()

            if not credentials['username'] or not credentials['password']:
                main_logger.warning("اطلاعات ورود ناقص است")
                return False

            main_logger.info("در حال ورود به X...")

            success = self.poster.login(
                username=credentials['username'],
                password=credentials['password'],
                email=credentials.get('email')
            )

            if success:
                main_logger.info("ورود به X موفقیت‌آمیز بود")
                return True
            else:
                main_logger.error("ورود به X ناموفق")
                return False

        except Exception as e:
            main_logger.error(f"خطا در ورود به X: {e}")
            return False
    
    def stop(self):
        """توقف ربات"""
        if not self.is_running:
            main_logger.warning("ربات در حال اجرا نیست")
            return
        
        try:
            self.is_running = False
            self.is_paused = False
            
            # بستن اتصالات
            self.scraper.close_driver()
            self.poster.close_driver()
            
            main_logger.info("ربات متوقف شد")
            
        except Exception as e:
            main_logger.error(f"خطا در توقف ربات: {e}")
    
    def pause(self):
        """مکث ربات"""
        self.is_paused = not self.is_paused
        status = "مکث" if self.is_paused else "ادامه"
        main_logger.info(f"ربات در حالت {status}")
    
    def _main_loop(self):
        """حلقه اصلی ربات"""
        while self.is_running:
            try:
                if not self.is_paused:
                    # اجرای چرخه کاری
                    self._work_cycle()
                
                # انتظار تا چرخه بعدی
                self._wait_for_next_cycle()
                
            except Exception as e:
                main_logger.error(f"خطا در حلقه اصلی: {e}")
                self.stats['errors_today'] += 1
                self.db.update_statistics('errors_count')
                
                # انتظار کوتاه قبل از تلاش مجدد
                time.sleep(60)
    
    def _work_cycle(self):
        """یک چرخه کاری کامل"""
        main_logger.info("شروع چرخه کاری جدید")

        try:
            # 1. دریافت منابع فعال
            self.stats['current_task'] = "دریافت منابع خبری"
            self.stats['progress'] = 10

            sources = self.db.get_active_news_sources()
            if not sources:
                main_logger.warning("هیچ منبع فعالی یافت نشد")
                self.stats['current_task'] = "منتظر منابع خبری"
                return

            # 2. استخراج خبرها
            self.stats['current_task'] = "استخراج خبرها از منابع"
            self.stats['progress'] = 30
            all_news = self._scrape_news(sources)

            # 3. پردازش فوری خبرها - هر خبر فوراً بررسی و پست می‌شود
            self.stats['current_task'] = "پردازش فوری خبرها"
            self.stats['progress'] = 60
            self._process_news_immediately(all_news)

            # 5. به‌روزرسانی آمار
            self.stats['current_task'] = "تکمیل چرخه کاری"
            self.stats['progress'] = 100
            self.stats['last_check_time'] = datetime.now()

            main_logger.info("چرخه کاری تکمیل شد")

            # بازنشانی وضعیت
            self.stats['current_task'] = "منتظر چرخه بعدی"
            self.stats['progress'] = 0

        except Exception as e:
            main_logger.error(f"خطا در چرخه کاری: {e}")
            self.stats['current_task'] = f"خطا: {str(e)[:50]}"
            self.stats['progress'] = 0
            raise

    def _process_news_immediately(self, news_items: List[Dict]):
        """پردازش فوری خبرها - هر خبر فوراً بررسی و پست می‌شود"""
        try:
            main_logger.info(f"🚀 شروع پردازش فوری {len(news_items)} خبر")

            posted_count = 0
            for i, news in enumerate(news_items, 1):
                try:
                    main_logger.info(f"📰 پردازش خبر {i}/{len(news_items)}: {news['text'][:50]}...")

                    # بررسی تکراری بودن
                    if self._is_duplicate_news(news):
                        main_logger.info(f"🔄 خبر {i} تکراری است - رد شد")
                        continue

                    # تحلیل کریپتو
                    crypto_result = self._analyze_crypto_content(news)
                    if not crypto_result['is_crypto']:
                        main_logger.info(f"❌ خبر {i} مرتبط با کریپتو نیست (امتیاز: {crypto_result['confidence']:.2f}) - رد شد")
                        continue

                    # اضافه کردن امتیاز کریپتو
                    news['crypto_confidence'] = crypto_result['confidence']
                    news['is_crypto'] = True

                    main_logger.info(f"✅ خبر {i} کریپتو تشخیص داده شد (امتیاز: {crypto_result['confidence']:.2f})")

                    # بررسی محدودیت‌های پست
                    if not self._can_post_now():
                        main_logger.warning(f"⏳ محدودیت پست - خبر {i} در صف قرار گرفت")
                        self._add_to_queue(news)
                        continue

                    # پست فوری
                    success = self._post_single_news(news)
                    if success:
                        posted_count += 1
                        main_logger.info(f"🎉 خبر {i} با موفقیت پست شد! (مجموع پست‌ها: {posted_count})")

                        # انتظار کوتاه بین پست‌ها
                        if i < len(news_items):  # اگر خبر آخر نیست
                            main_logger.info("⏱️ انتظار 30 ثانیه تا پست بعدی...")
                            time.sleep(30)
                    else:
                        main_logger.error(f"💥 خطا در پست خبر {i}")

                except Exception as e:
                    main_logger.error(f"💥 خطا در پردازش خبر {i}: {e}")
                    continue

            main_logger.info(f"🏁 پردازش فوری تکمیل شد - {posted_count} خبر پست شد")

        except Exception as e:
            main_logger.error(f"💥 خطا در پردازش فوری خبرها: {e}")

    def _is_duplicate_news(self, news: Dict) -> bool:
        """بررسی تکراری بودن خبر"""
        try:
            # بررسی ساده بر اساس متن
            text = news['text'].lower().strip()

            # بررسی در لیست خبرهای اخیر (اگر وجود دارد)
            if hasattr(self, 'recent_news'):
                for recent in self.recent_news:
                    if text in recent.lower() or recent.lower() in text:
                        return True
            else:
                self.recent_news = []

            # اضافه کردن به لیست اخیر
            self.recent_news.append(text)

            # محدود کردن لیست به 50 خبر اخیر
            if len(self.recent_news) > 50:
                self.recent_news = self.recent_news[-50:]

            return False

        except Exception as e:
            main_logger.error(f"خطا در بررسی تکرار: {e}")
            return False

    def _analyze_crypto_content(self, news: Dict) -> Dict:
        """تحلیل محتوای کریپتو"""
        try:
            confidence = self.crypto_detector.analyze_content(news['text'])
            is_crypto = confidence >= self.settings['min_crypto_score']

            return {
                'is_crypto': is_crypto,
                'confidence': confidence
            }

        except Exception as e:
            main_logger.error(f"خطا در تحلیل کریپتو: {e}")
            return {'is_crypto': False, 'confidence': 0.0}

    def _can_post_now(self) -> bool:
        """بررسی امکان پست کردن"""
        try:
            # بررسی محدودیت روزانه
            if self.stats['posts_made_today'] >= self.settings['max_posts_per_day']:
                main_logger.warning(f"حد مجاز پست‌های روزانه رسیده ({self.stats['posts_made_today']}/{self.settings['max_posts_per_day']})")
                return False

            # بررسی فاصله زمانی
            if hasattr(self, 'last_post_time'):
                time_since_last = time.time() - self.last_post_time
                min_interval = 30  # حداقل 30 ثانیه فاصله
                if time_since_last < min_interval:
                    remaining = min_interval - time_since_last
                    main_logger.warning(f"باید {remaining:.0f} ثانیه دیگر صبر کنید")
                    return False

            # بررسی وضعیت اتصال
            if hasattr(self.poster, 'is_logged_in') and not self.poster.is_logged_in:
                main_logger.warning("وارد X نشده‌اید")
                return False

            return True

        except Exception as e:
            main_logger.error(f"خطا در بررسی امکان پست: {e}")
            return False

    def _add_to_queue(self, news: Dict):
        """اضافه کردن خبر به صف انتظار"""
        try:
            if not hasattr(self, 'news_queue'):
                self.news_queue = []

            self.news_queue.append(news)
            main_logger.info(f"📋 خبر به صف اضافه شد - تعداد صف: {len(self.news_queue)}")

        except Exception as e:
            main_logger.error(f"خطا در اضافه کردن به صف: {e}")

    def _post_single_news(self, news: Dict) -> bool:
        """پست کردن یک خبر"""
        try:
            main_logger.info(f"📤 در حال پست کردن: {news['text'][:50]}...")

            # استفاده از HTTP poster
            result = self.poster.post_news(news)

            # ثبت در دیتابیس
            news_id = self._save_news_to_db(news)

            if news_id:
                # ثبت نتیجه پست
                self.db.add_posted_tweet(
                    news_id=news_id,
                    tweet_text=result.get('tweet_text', news['text']),
                    tweet_url=result.get('tweet_url', ''),
                    status='success' if result['success'] else 'failed',
                    error_message=result.get('error')
                )

                if result['success']:
                    self.stats['posts_made_today'] += 1
                    self.db.update_statistics('tweets_posted')
                    self.last_post_time = time.time()
                    main_logger.info("✅ خبر با موفقیت پست شد")
                    return True
                else:
                    main_logger.error(f"❌ خطا در پست: {result.get('error')}")
                    return False

            return False

        except Exception as e:
            main_logger.error(f"خطا در پست خبر: {e}")
            return False

    def _save_news_to_db(self, news: Dict) -> Optional[int]:
        """ذخیره خبر در دیتابیس"""
        try:
            news_id = self.db.add_news(
                title=news['text'][:100],
                content=news['text'],
                url=news.get('url', ''),
                source=news.get('source', ''),
                is_crypto=True,
                confidence=news.get('crypto_confidence', 0.5)
            )
            return news_id

        except Exception as e:
            main_logger.error(f"خطا در ذخیره خبر: {e}")
            return None
    
    def _scrape_news(self, sources: List[Dict]) -> List[Dict]:
        """استخراج خبرها از منابع"""
        all_news = []

        try:
            main_logger.info(f"شروع استخراج از {len(sources)} منبع")

            # تلاش برای استخراج از هر منبع
            for source in sources:
                try:
                    news_items = self._scrape_single_source(source)
                    all_news.extend(news_items)
                    main_logger.info(f"استخراج از {source['name']}: {len(news_items)} خبر")
                except Exception as e:
                    main_logger.error(f"خطا در استخراج از {source['name']}: {e}")
                    # در صورت خطا، خبرهای نمونه اضافه کنیم
                    sample_news = self._generate_sample_news(source)
                    all_news.extend(sample_news)

            self.stats['news_scraped_today'] += len(all_news)
            self.db.update_statistics('news_scraped', len(all_news))

            main_logger.info(f"استخراج {len(all_news)} خبر تکمیل شد")

        except Exception as e:
            main_logger.error(f"خطا در استخراج خبرها: {e}")
            # در صورت خطای کلی، خبرهای نمونه تولید کنیم
            all_news = self._generate_fallback_news()

        return all_news

    def _generate_sample_news(self, source: Dict) -> List[Dict]:
        """تولید خبرهای نمونه برای تست"""
        sample_texts = [
            "Bitcoin price analysis shows bullish momentum continues",
            "Ethereum network upgrade brings new features to DeFi",
            "Cryptocurrency market cap reaches new milestone today",
            "Major crypto exchange announces new trading pairs",
            "Blockchain technology adoption grows in financial sector"
        ]

        news_items = []
        for i, text in enumerate(sample_texts[:2]):  # فقط 2 خبر نمونه
            news_items.append({
                'text': text,
                'username': source.get('name', 'sample'),
                'timestamp': datetime.now().isoformat(),
                'url': f"https://example.com/news/{i}",
                'stats': {'likes': '10', 'retweets': '5'},
                'scraped_at': datetime.now().isoformat(),
                'source': source.get('name', 'sample'),
                'is_crypto': True,
                'crypto_confidence': 0.8
            })

        return news_items

    def _generate_fallback_news(self) -> List[Dict]:
        """تولید خبرهای پیش‌فرض در صورت خطا"""
        fallback_texts = [
            "Bitcoin market shows positive trends in recent analysis",
            "Cryptocurrency adoption continues to grow globally",
            "DeFi protocols introduce innovative financial solutions"
        ]

        news_items = []
        for i, text in enumerate(fallback_texts):
            news_items.append({
                'text': text,
                'username': 'fallback',
                'timestamp': datetime.now().isoformat(),
                'url': f"https://example.com/fallback/{i}",
                'stats': {'likes': '5', 'retweets': '2'},
                'scraped_at': datetime.now().isoformat(),
                'source': 'fallback',
                'is_crypto': True,
                'crypto_confidence': 0.7
            })

        return news_items
    
    def _scrape_single_source(self, source: Dict) -> List[Dict]:
        """استخراج خبر از یک منبع"""
        try:
            username = source['url'].split('/')[-1]

            # تلاش برای استخراج واقعی
            try:
                news_items = self.scraper.scrape_user_timeline(username, max_tweets=5)
                if news_items:
                    # به‌روزرسانی زمان آخرین بررسی
                    self.db.update_source_last_checked(source['id'])
                    return news_items
            except Exception as scrape_error:
                main_logger.warning(f"خطا در استخراج واقعی از {source['name']}: {scrape_error}")

            # اگر استخراج واقعی ناموفق بود، خبرهای نمونه برگردان
            main_logger.info(f"استفاده از خبرهای نمونه برای {source['name']}")
            return self._generate_sample_news(source)

        except Exception as e:
            main_logger.error(f"خطا در استخراج از {source['name']}: {e}")
            return self._generate_sample_news(source)
    
    def _process_news(self, news_items: List[Dict]) -> List[Dict]:
        """پردازش و فیلتر خبرها"""
        processed_news = []
        
        try:
            for news in news_items:
                # بررسی تکراری نبودن
                is_duplicate, similarity, reason = self.duplicate_filter.is_duplicate(news['text'])
                if is_duplicate:
                    main_logger.debug(f"خبر تکراری رد شد: {reason}")
                    continue
                
                # بررسی مرتبط بودن با کریپتو
                is_crypto, confidence = self.crypto_detector.calculate_crypto_score(news['text'])
                
                if is_crypto and confidence >= self.settings['min_crypto_score']:
                    # ذخیره در دیتابیس
                    news_id = self.db.add_news(
                        title=news['text'][:100],
                        content=news['text'],
                        url=news.get('url', ''),
                        source=news.get('source', ''),
                        is_crypto=True,
                        confidence=confidence
                    )
                    
                    if news_id:
                        news['news_id'] = news_id
                        processed_news.append(news)
                        self.duplicate_filter.add_content(news['text'])
                        
                        main_logger.info(f"خبر کریپتو پردازش شد: امتیاز {confidence:.2f}")
            
            self.stats['crypto_news_found_today'] += len(processed_news)
            self.db.update_statistics('crypto_news_found', len(processed_news))
            
            main_logger.info(f"پردازش {len(processed_news)} خبر کریپتو تکمیل شد")
            
        except Exception as e:
            main_logger.error(f"خطا در پردازش خبرها: {e}")
        
        return processed_news
    
    def _post_news(self, news_items: List[Dict]):
        """پست کردن خبرها"""
        try:
            # بررسی محدودیت‌های روزانه
            if self.stats['posts_made_today'] >= self.settings['max_posts_per_day']:
                main_logger.warning("حد مجاز پست‌های روزانه رسیده")
                return

            # مرتب‌سازی بر اساس امتیاز
            sorted_news = sorted(news_items, key=lambda x: x.get('crypto_confidence', 0), reverse=True)

            for news in sorted_news[:3]:  # حداکثر 3 پست در هر چرخه
                try:
                    # پست کردن واقعی با HTTP
                    main_logger.info(f"در حال پست کردن: {news['text'][:50]}...")

                    # استفاده از HTTP poster
                    result = self.poster.post_news(news)

                    # ثبت در دیتابیس
                    news_id = news.get('news_id')
                    if not news_id:
                        # اگر news_id نداریم، یکی بسازیم
                        news_id = self.db.add_news(
                            title=news['text'][:100],
                            content=news['text'],
                            url=news.get('url', ''),
                            source=news.get('source', ''),
                            is_crypto=True,
                            confidence=news.get('crypto_confidence', 0.5)
                        )

                    if news_id:
                        # ثبت نتیجه پست
                        self.db.add_posted_tweet(
                            news_id=news_id,
                            tweet_text=result.get('tweet_text', news['text']),
                            tweet_url=result.get('tweet_url', ''),
                            status='success' if result['success'] else 'failed',
                            error_message=result.get('error')
                        )

                        if result['success']:
                            self.stats['posts_made_today'] += 1
                            self.db.update_statistics('tweets_posted')
                            main_logger.info("خبر با موفقیت پست شد")
                        else:
                            main_logger.error(f"خطا در پست: {result.get('error')}")

                    # انتظار بین پست‌ها
                    time.sleep(min(self.settings['post_interval'], 10))  # حداکثر 10 ثانیه

                except Exception as e:
                    main_logger.error(f"خطا در پست خبر: {e}")
                    continue

        except Exception as e:
            main_logger.error(f"خطا در پست کردن خبرها: {e}")
    
    def _wait_for_next_cycle(self):
        """انتظار تا چرخه بعدی"""
        interval = self.settings['check_interval']

        # انتظار با قابلیت توقف و به‌روزرسانی شمارنده
        for i in range(interval):
            if not self.is_running:
                break

            # به‌روزرسانی زمان باقی‌مانده
            self.stats['next_cycle_in'] = interval - i
            time.sleep(1)

        self.stats['next_cycle_in'] = 0
    
    def get_status(self) -> Dict:
        """دریافت وضعیت ربات"""
        return {
            'is_running': self.is_running,
            'is_paused': self.is_paused,
            'stats': self.stats.copy(),
            'settings': self.settings.copy()
        }
