"""
مدیریت اصلی ربات
"""

import time
import threading
from datetime import datetime
from typing import List, Dict, Optional
from concurrent.futures import ThreadPoolExecutor

from crypto_news_bot.database.models import DatabaseManager
from crypto_news_bot.scraper.x_scraper import XS<PERSON>raper
from crypto_news_bot.poster.x_poster import XPoster
from crypto_news_bot.content_filter.crypto_detector import CryptoContentDetector
from crypto_news_bot.content_filter.duplicate_filter import DuplicateContentFilter
from crypto_news_bot.utils.logger import main_logger
from crypto_news_bot.config import SCRAPING_INTERVAL, DEFAULT_NEWS_SOURCES


class BotManager:
    """کلاس مدیریت اصلی ربات"""
    
    def __init__(self):
        self.db = DatabaseManager()
        self.scraper = XScraper()
        self.poster = XPoster()
        self.crypto_detector = CryptoContentDetector()
        self.duplicate_filter = DuplicateContentFilter()
        
        self.is_running = False
        self.is_paused = False
        self.worker_thread = None
        self.executor = ThreadPoolExecutor(max_workers=3)
        
        # آمار
        self.stats = {
            'news_scraped_today': 0,
            'crypto_news_found_today': 0,
            'posts_made_today': 0,
            'errors_today': 0,
            'last_check_time': None,
            'current_task': None,
            'progress': 0,
            'next_cycle_in': 0
        }
        
        # تنظیمات
        self.settings = {
            'check_interval': SCRAPING_INTERVAL * 60,  # تبدیل به ثانیه
            'max_posts_per_day': 50,
            'min_crypto_score': 0.3,
            'post_interval': 300  # 5 دقیقه
        }
        
        self.load_settings()
        self.initialize_sources()
    
    def load_settings(self):
        """بارگذاری تنظیمات از دیتابیس"""
        try:
            self.settings['check_interval'] = int(self.db.get_setting('check_interval', '5')) * 60
            self.settings['max_posts_per_day'] = int(self.db.get_setting('max_posts_daily', '50'))
            self.settings['min_crypto_score'] = float(self.db.get_setting('min_crypto_score', '0.3'))
            self.settings['post_interval'] = int(self.db.get_setting('post_interval', '5')) * 60

            main_logger.info(f"تنظیمات بارگذاری شد: فاصله بررسی {self.settings['check_interval']//60} دقیقه، حداکثر پست {self.settings['max_posts_per_day']}")

        except Exception as e:
            main_logger.error(f"خطا در بارگذاری تنظیمات: {e}")

    def reload_settings(self):
        """بارگذاری مجدد تنظیمات"""
        self.load_settings()

        # بارگذاری مجدد تنظیمات در اجزای دیگر
        if hasattr(self, 'crypto_detector'):
            self.crypto_detector.load_settings()

        main_logger.info("تنظیمات مجدداً بارگذاری شد")
    
    def initialize_sources(self):
        """مقداردهی اولیه منابع خبری"""
        try:
            existing_sources = self.db.get_active_news_sources()
            
            if not existing_sources:
                # اضافه کردن منابع پیش‌فرض
                for source_url in DEFAULT_NEWS_SOURCES:
                    source_name = source_url.split('/')[-1]
                    self.db.add_news_source(source_name, source_url)
                
                main_logger.info("منابع پیش‌فرض اضافه شدند")
            
        except Exception as e:
            main_logger.error(f"خطا در مقداردهی منابع: {e}")
    
    def start(self):
        """شروع ربات"""
        if self.is_running:
            main_logger.warning("ربات در حال اجرا است")
            return
        
        try:
            self.is_running = True
            self.is_paused = False
            
            main_logger.info("ربات شروع شد")
            
            # شروع حلقه اصلی در thread جداگانه
            self.worker_thread = threading.Thread(target=self._main_loop, daemon=True)
            self.worker_thread.start()
            
        except Exception as e:
            main_logger.error(f"خطا در شروع ربات: {e}")
            self.is_running = False
    
    def stop(self):
        """توقف ربات"""
        if not self.is_running:
            main_logger.warning("ربات در حال اجرا نیست")
            return
        
        try:
            self.is_running = False
            self.is_paused = False
            
            # بستن اتصالات
            self.scraper.close_driver()
            self.poster.close_driver()
            
            main_logger.info("ربات متوقف شد")
            
        except Exception as e:
            main_logger.error(f"خطا در توقف ربات: {e}")
    
    def pause(self):
        """مکث ربات"""
        self.is_paused = not self.is_paused
        status = "مکث" if self.is_paused else "ادامه"
        main_logger.info(f"ربات در حالت {status}")
    
    def _main_loop(self):
        """حلقه اصلی ربات"""
        while self.is_running:
            try:
                if not self.is_paused:
                    # اجرای چرخه کاری
                    self._work_cycle()
                
                # انتظار تا چرخه بعدی
                self._wait_for_next_cycle()
                
            except Exception as e:
                main_logger.error(f"خطا در حلقه اصلی: {e}")
                self.stats['errors_today'] += 1
                self.db.update_statistics('errors_count')
                
                # انتظار کوتاه قبل از تلاش مجدد
                time.sleep(60)
    
    def _work_cycle(self):
        """یک چرخه کاری کامل"""
        main_logger.info("شروع چرخه کاری جدید")

        try:
            # 1. دریافت منابع فعال
            self.stats['current_task'] = "دریافت منابع خبری"
            self.stats['progress'] = 10

            sources = self.db.get_active_news_sources()
            if not sources:
                main_logger.warning("هیچ منبع فعالی یافت نشد")
                self.stats['current_task'] = "منتظر منابع خبری"
                return

            # 2. استخراج خبرها
            self.stats['current_task'] = "استخراج خبرها از منابع"
            self.stats['progress'] = 30
            all_news = self._scrape_news(sources)

            # 3. پردازش و فیلتر خبرها
            self.stats['current_task'] = "تحلیل و فیلتر خبرها"
            self.stats['progress'] = 60
            processed_news = self._process_news(all_news)

            # 4. پست کردن خبرهای مناسب
            self.stats['current_task'] = "پست کردن خبرها"
            self.stats['progress'] = 80
            self._post_news(processed_news)

            # 5. به‌روزرسانی آمار
            self.stats['current_task'] = "تکمیل چرخه کاری"
            self.stats['progress'] = 100
            self.stats['last_check_time'] = datetime.now()

            main_logger.info("چرخه کاری تکمیل شد")

            # بازنشانی وضعیت
            self.stats['current_task'] = "منتظر چرخه بعدی"
            self.stats['progress'] = 0

        except Exception as e:
            main_logger.error(f"خطا در چرخه کاری: {e}")
            self.stats['current_task'] = f"خطا: {str(e)[:50]}"
            self.stats['progress'] = 0
            raise
    
    def _scrape_news(self, sources: List[Dict]) -> List[Dict]:
        """استخراج خبرها از منابع"""
        all_news = []
        
        try:
            main_logger.info(f"شروع استخراج از {len(sources)} منبع")
            
            # استخراج موازی از منابع
            futures = []
            for source in sources:
                future = self.executor.submit(self._scrape_single_source, source)
                futures.append(future)
            
            # جمع‌آوری نتایج
            for future in futures:
                try:
                    news_items = future.result(timeout=300)  # 5 دقیقه timeout
                    all_news.extend(news_items)
                except Exception as e:
                    main_logger.error(f"خطا در استخراج از منبع: {e}")
            
            self.stats['news_scraped_today'] += len(all_news)
            self.db.update_statistics('news_scraped', len(all_news))
            
            main_logger.info(f"استخراج {len(all_news)} خبر تکمیل شد")
            
        except Exception as e:
            main_logger.error(f"خطا در استخراج خبرها: {e}")
        
        return all_news
    
    def _scrape_single_source(self, source: Dict) -> List[Dict]:
        """استخراج خبر از یک منبع"""
        try:
            username = source['url'].split('/')[-1]
            news_items = self.scraper.scrape_user_timeline(username, max_tweets=10)
            
            # به‌روزرسانی زمان آخرین بررسی
            self.db.update_source_last_checked(source['id'])
            
            return news_items
            
        except Exception as e:
            main_logger.error(f"خطا در استخراج از {source['name']}: {e}")
            return []
    
    def _process_news(self, news_items: List[Dict]) -> List[Dict]:
        """پردازش و فیلتر خبرها"""
        processed_news = []
        
        try:
            for news in news_items:
                # بررسی تکراری نبودن
                is_duplicate, similarity, reason = self.duplicate_filter.is_duplicate(news['text'])
                if is_duplicate:
                    main_logger.debug(f"خبر تکراری رد شد: {reason}")
                    continue
                
                # بررسی مرتبط بودن با کریپتو
                is_crypto, confidence = self.crypto_detector.calculate_crypto_score(news['text'])
                
                if is_crypto and confidence >= self.settings['min_crypto_score']:
                    # ذخیره در دیتابیس
                    news_id = self.db.add_news(
                        title=news['text'][:100],
                        content=news['text'],
                        url=news.get('url', ''),
                        source=news.get('source', ''),
                        is_crypto=True,
                        confidence=confidence
                    )
                    
                    if news_id:
                        news['news_id'] = news_id
                        processed_news.append(news)
                        self.duplicate_filter.add_content(news['text'])
                        
                        main_logger.info(f"خبر کریپتو پردازش شد: امتیاز {confidence:.2f}")
            
            self.stats['crypto_news_found_today'] += len(processed_news)
            self.db.update_statistics('crypto_news_found', len(processed_news))
            
            main_logger.info(f"پردازش {len(processed_news)} خبر کریپتو تکمیل شد")
            
        except Exception as e:
            main_logger.error(f"خطا در پردازش خبرها: {e}")
        
        return processed_news
    
    def _post_news(self, news_items: List[Dict]):
        """پست کردن خبرها"""
        try:
            # بررسی محدودیت‌های روزانه
            if self.stats['posts_made_today'] >= self.settings['max_posts_per_day']:
                main_logger.warning("حد مجاز پست‌های روزانه رسیده")
                return
            
            # مرتب‌سازی بر اساس امتیاز
            sorted_news = sorted(news_items, key=lambda x: x.get('crypto_confidence', 0), reverse=True)
            
            for news in sorted_news[:5]:  # حداکثر 5 پست در هر چرخه
                try:
                    # پست کردن
                    result = self.poster.post_news(news)
                    
                    # ثبت نتیجه
                    self.db.add_posted_tweet(
                        news_id=news['news_id'],
                        tweet_text=result.get('tweet_text', ''),
                        tweet_url=result.get('tweet_url'),
                        status='success' if result['success'] else 'failed',
                        error_message=result.get('error')
                    )
                    
                    if result['success']:
                        self.stats['posts_made_today'] += 1
                        self.db.update_statistics('tweets_posted')
                        main_logger.info("خبر با موفقیت پست شد")
                    else:
                        main_logger.error(f"خطا در پست: {result.get('error')}")
                    
                    # انتظار بین پست‌ها
                    time.sleep(self.settings['post_interval'])
                    
                except Exception as e:
                    main_logger.error(f"خطا در پست خبر: {e}")
                    continue
            
        except Exception as e:
            main_logger.error(f"خطا در پست کردن خبرها: {e}")
    
    def _wait_for_next_cycle(self):
        """انتظار تا چرخه بعدی"""
        interval = self.settings['check_interval']

        # انتظار با قابلیت توقف و به‌روزرسانی شمارنده
        for i in range(interval):
            if not self.is_running:
                break

            # به‌روزرسانی زمان باقی‌مانده
            self.stats['next_cycle_in'] = interval - i
            time.sleep(1)

        self.stats['next_cycle_in'] = 0
    
    def get_status(self) -> Dict:
        """دریافت وضعیت ربات"""
        return {
            'is_running': self.is_running,
            'is_paused': self.is_paused,
            'stats': self.stats.copy(),
            'settings': self.settings.copy()
        }
