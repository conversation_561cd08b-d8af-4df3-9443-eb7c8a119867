"""
سیستم لاگ‌گیری پیشرفته
"""

import logging
import colorlog
from pathlib import Path
from crypto_news_bot.config import LOGS_DIR, LOG_LEVEL, LOG_FORMAT


def setup_logger(name: str, log_file: str = None) -> logging.Logger:
    """
    تنظیم logger با قابلیت نمایش رنگی در کنسول و ذخیره در فایل
    
    Args:
        name: نام logger
        log_file: نام فایل لاگ (اختیاری)
    
    Returns:
        logging.Logger: logger پیکربندی شده
    """
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, LOG_LEVEL))
    
    # جلوگیری از تکرار handler ها
    if logger.handlers:
        return logger
    
    # فرمت رنگی برای کنسول
    console_formatter = colorlog.ColoredFormatter(
        "%(log_color)s%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
        log_colors={
            'DEBUG': 'cyan',
            'INFO': 'green',
            'WARNING': 'yellow',
            'ERROR': 'red',
            'CRITICAL': 'red,bg_white',
        }
    )
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)
    
    # File handler (اگر نام فایل مشخص شده باشد)
    if log_file:
        log_path = LOGS_DIR / log_file
        file_handler = logging.FileHandler(log_path, encoding='utf-8')
        file_formatter = logging.Formatter(LOG_FORMAT)
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)
    
    return logger


# Logger های اصلی
main_logger = setup_logger("CryptoBot", "main.log")
scraper_logger = setup_logger("Scraper", "scraper.log")
poster_logger = setup_logger("Poster", "poster.log")
gui_logger = setup_logger("GUI", "gui.log")
