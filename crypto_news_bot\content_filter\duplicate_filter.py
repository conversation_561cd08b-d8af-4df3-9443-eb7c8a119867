"""
سیستم تشخیص و فیلتر محتوای تکراری
"""

import hashlib
import difflib
from typing import List, Dict, Tuple, Set
from datetime import datetime, timedelta
import re

from crypto_news_bot.utils.logger import main_logger


class DuplicateContentFilter:
    """کلاس تشخیص محتوای تکراری"""
    
    def __init__(self, similarity_threshold: float = 0.8):
        self.similarity_threshold = similarity_threshold
        self.content_hashes = set()
        self.content_fingerprints = []
        self.max_history = 1000  # حداکثر تعداد محتوای ذخیره شده
    
    def normalize_text(self, text: str) -> str:
        """نرمال‌سازی متن برای مقایسه"""
        # حذف URL ها
        text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
        
        # حذف منشن‌ها و هشتگ‌ها
        text = re.sub(r'@\w+|#\w+', '', text)
        
        # حذف اعداد و تاریخ‌ها
        text = re.sub(r'\d+', '', text)
        
        # حذف علائم نگارشی اضافی
        text = re.sub(r'[^\w\s]', ' ', text)
        
        # حذف فاصله‌های اضافی
        text = re.sub(r'\s+', ' ', text.strip())
        
        return text.lower()
    
    def generate_content_hash(self, text: str) -> str:
        """تولید hash برای محتوا"""
        normalized = self.normalize_text(text)
        return hashlib.md5(normalized.encode('utf-8')).hexdigest()
    
    def generate_fingerprint(self, text: str) -> List[str]:
        """تولید fingerprint برای تشخیص شباهت"""
        normalized = self.normalize_text(text)
        words = normalized.split()
        
        # تولید n-gram ها
        fingerprint = []
        
        # 2-gram
        for i in range(len(words) - 1):
            fingerprint.append(f"{words[i]}_{words[i+1]}")
        
        # 3-gram
        for i in range(len(words) - 2):
            fingerprint.append(f"{words[i]}_{words[i+1]}_{words[i+2]}")
        
        return fingerprint
    
    def calculate_similarity(self, text1: str, text2: str) -> float:
        """محاسبه شباهت بین دو متن"""
        # روش 1: مقایسه مستقیم متن نرمال شده
        norm1 = self.normalize_text(text1)
        norm2 = self.normalize_text(text2)
        
        if norm1 == norm2:
            return 1.0
        
        # روش 2: استفاده از difflib
        similarity = difflib.SequenceMatcher(None, norm1, norm2).ratio()
        
        # روش 3: مقایسه fingerprint ها
        fp1 = set(self.generate_fingerprint(text1))
        fp2 = set(self.generate_fingerprint(text2))
        
        if fp1 and fp2:
            jaccard_similarity = len(fp1.intersection(fp2)) / len(fp1.union(fp2))
            # ترکیب دو روش
            similarity = max(similarity, jaccard_similarity)
        
        return similarity
    
    def is_duplicate(self, text: str, check_history: bool = True) -> Tuple[bool, float, str]:
        """
        بررسی تکراری بودن محتوا
        
        Returns:
            Tuple[bool, float, str]: (is_duplicate, max_similarity, reason)
        """
        content_hash = self.generate_content_hash(text)
        
        # بررسی hash دقیق
        if content_hash in self.content_hashes:
            return True, 1.0, "exact_hash_match"
        
        if not check_history:
            return False, 0.0, "no_check"
        
        # بررسی شباهت با محتوای قبلی
        max_similarity = 0.0
        similar_content = ""
        
        for stored_content in self.content_fingerprints[-100:]:  # بررسی 100 محتوای آخر
            similarity = self.calculate_similarity(text, stored_content['text'])
            if similarity > max_similarity:
                max_similarity = similarity
                similar_content = stored_content['text'][:100] + "..."
        
        is_duplicate = max_similarity >= self.similarity_threshold
        reason = f"similarity_{max_similarity:.2f}" if is_duplicate else "unique"
        
        return is_duplicate, max_similarity, reason
    
    def add_content(self, text: str, metadata: Dict = None) -> bool:
        """اضافه کردن محتوا به تاریخچه"""
        try:
            content_hash = self.generate_content_hash(text)
            
            # اضافه کردن hash
            self.content_hashes.add(content_hash)
            
            # اضافه کردن fingerprint
            content_data = {
                'text': text,
                'hash': content_hash,
                'timestamp': datetime.now(),
                'metadata': metadata or {}
            }
            
            self.content_fingerprints.append(content_data)
            
            # محدود کردن تاریخچه
            if len(self.content_fingerprints) > self.max_history:
                # حذف قدیمی‌ترین موارد
                removed = self.content_fingerprints.pop(0)
                self.content_hashes.discard(removed['hash'])
            
            return True
            
        except Exception as e:
            main_logger.error(f"خطا در اضافه کردن محتوا: {e}")
            return False
    
    def clean_old_content(self, days: int = 7) -> int:
        """پاک کردن محتوای قدیمی"""
        cutoff_date = datetime.now() - timedelta(days=days)
        removed_count = 0
        
        # فیلتر کردن محتوای قدیمی
        new_fingerprints = []
        new_hashes = set()
        
        for content in self.content_fingerprints:
            if content['timestamp'] > cutoff_date:
                new_fingerprints.append(content)
                new_hashes.add(content['hash'])
            else:
                removed_count += 1
        
        self.content_fingerprints = new_fingerprints
        self.content_hashes = new_hashes
        
        main_logger.info(f"پاک شد {removed_count} محتوای قدیمی")
        return removed_count
    
    def get_statistics(self) -> Dict:
        """دریافت آمار فیلتر"""
        return {
            'total_content': len(self.content_fingerprints),
            'unique_hashes': len(self.content_hashes),
            'similarity_threshold': self.similarity_threshold,
            'max_history': self.max_history
        }
    
    def update_threshold(self, new_threshold: float) -> bool:
        """به‌روزرسانی آستانه شباهت"""
        if 0.0 <= new_threshold <= 1.0:
            self.similarity_threshold = new_threshold
            main_logger.info(f"آستانه شباهت به {new_threshold} تغییر یافت")
            return True
        return False


class ContentQualityFilter:
    """فیلتر کیفیت محتوا"""
    
    def __init__(self):
        self.min_length = 10
        self.max_length = 500
        self.spam_keywords = {
            'click here', 'buy now', 'limited time', 'act fast', 'don\'t miss',
            'free money', 'guaranteed profit', 'get rich quick', 'easy money'
        }
    
    def check_quality(self, text: str) -> Tuple[bool, List[str]]:
        """
        بررسی کیفیت محتوا
        
        Returns:
            Tuple[bool, List[str]]: (is_quality, issues)
        """
        issues = []
        
        # بررسی طول
        if len(text.strip()) < self.min_length:
            issues.append(f"متن کوتاه‌تر از {self.min_length} کاراکتر")
        
        if len(text.strip()) > self.max_length:
            issues.append(f"متن بلندتر از {self.max_length} کاراکتر")
        
        # بررسی اسپم
        text_lower = text.lower()
        spam_found = [keyword for keyword in self.spam_keywords 
                     if keyword in text_lower]
        if spam_found:
            issues.append(f"کلمات اسپم: {', '.join(spam_found)}")
        
        # بررسی تکرار کاراکتر
        if re.search(r'(.)\1{4,}', text):
            issues.append("تکرار بیش از حد کاراکتر")
        
        # بررسی حروف بزرگ
        upper_ratio = sum(1 for c in text if c.isupper()) / max(len(text), 1)
        if upper_ratio > 0.5:
            issues.append("استفاده بیش از حد از حروف بزرگ")
        
        is_quality = len(issues) == 0
        return is_quality, issues
