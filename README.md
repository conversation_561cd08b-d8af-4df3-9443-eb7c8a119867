# 🤖 ربات خبرهای کریپتو

ربات هوشمند برای پست خودکار خبرهای کریپتوکارنسی در X (تویتر)

## 🚀 اجرای سریع

```bash
# دوبار کلیک روی فایل
start_crypto_bot.bat

# یا
python start_bot.py
```

## ⚙️ تنظیمات اولیه

1. **فایل .env ایجاد کنید:**
```
X_USERNAME=نام_کاربری_شما
X_PASSWORD=رمز_عبور_شما
X_EMAIL=ایمیل_شما@example.com
```

2. **در برنامه:**
   - به تب "تنظیمات" بروید
   - کلمات کلیدی و نمادهای کریپتو را تنظیم کنید
   - منابع خبری اضافه کنید
   - روی "شروع ربات" کلیک کنید

## 📱 ویژگی‌ها

- ✅ **تشخیص هوشمند** خبرهای کریپتو
- ✅ **رابط گرافیکی** کامل
- ✅ **آمار زنده** و نمودارها  
- ✅ **مانیتورینگ سیستم**
- ✅ **فیلتر تکرار** خودکار
- ✅ **تنظیمات قابل تغییر**

## 🔧 تنظیمات

### کلمات کلیدی (هر خط یک کلمه):
```
bitcoin
ethereum
crypto
بیت کوین
اتریوم
```

### نمادهای کریپتو (جدا شده با کاما):
```
BTC, ETH, BNB, XRP, ADA, SOL, DOGE
```

## ⚠️ نکات

- حداکثر 50 پست در روز
- فاصله 5 دقیقه بین بررسی‌ها
- از قوانین X پیروی کنید
