# 🤖 ربات هوشمند خبرهای کریپتو

ربات پیشرفته برای جمع‌آوری و پست خودکار خبرهای کریپتوکارنسی در X (تویتر)

## ✨ ویژگی‌های کلیدی

- 🧠 **تشخیص هوشمند** خبرهای کریپتو با الگوریتم پیشرفته
- 🌐 **بدون مرورگر** - استفاده از HTTP requests مستقیم
- 🔐 **ورود خودکار** به X با مدیریت session
- 📊 **رابط گرافیکی** کامل با نمودارها و آمار زنده
- 🔄 **فیلتر تکرار** خودکار خبرها
- ⚙️ **تنظیمات قابل تغییر** و ذخیره‌سازی امن

## 🚀 نصب و اجرا

### روش سریع:
```bash
python start_bot.py
```

### یا دوبار کلیک روی:
```
start_crypto_bot.bat
```

## ⚙️ تنظیمات اولیه

### 1. تنظیم حساب X:
- منو "فایل" → "تنظیمات"
- وارد کردن نام کاربری، ایمیل، رمز عبور
- کلیک "تست اطلاعات حساب"
- ذخیره تنظیمات

### 2. تنظیم کلمات کلیدی:
```
bitcoin
ethereum
crypto
cryptocurrency
blockchain
defi
nft
trading
بیت کوین
اتریوم
کریپتو
رمزارز
```

### 3. تنظیم نمادهای کریپتو:
```
BTC, ETH, BNB, XRP, ADA, SOL, DOGE, DOT, AVAX, SHIB, MATIC, LTC, UNI, LINK
```

## 📱 رابط کاربری

### تب‌های موجود:
- **🎮 کنترل ربات**: شروع/توقف + تنظیمات سریع
- **📊 آمار زنده**: نمودارها و آمار زمان واقعی
- **🔴 فعالیت زنده**: مشاهده کارهای جاری
- **💻 وضعیت سیستم**: مانیتورینگ عملکرد
- **🐦 وضعیت X**: کنترل اتصال و آمار پست‌ها
- **📰 منابع خبری**: مدیریت منابع
- **📝 لاگ‌ها**: مشاهده تمام فعالیت‌ها

## 🎯 نحوه استفاده

1. **راه‌اندازی اولیه:**
   - اجرای برنامه
   - تنظیم اطلاعات X
   - اضافه کردن منابع خبری

2. **شروع کار:**
   - کلیک "شروع ربات"
   - ورود خودکار به X
   - شروع جمع‌آوری خبرها

3. **مانیتورینگ:**
   - مشاهده آمار در تب‌های مختلف
   - کنترل وضعیت اتصال X
   - بررسی لاگ‌های فعالیت

## 🔧 ویژگی‌های فنی

### سیستم HTTP-based:
- ✅ بدون نیاز به Chrome driver
- ✅ سرعت بالا و مصرف منابع کم
- ✅ پایداری بیشتر
- ✅ مدیریت session و توکن‌ها

### امنیت:
- 🔒 رمزگذاری رمز عبور
- 🔐 مدیریت امن توکن‌ها
- 🛡️ کنترل نرخ درخواست‌ها

### هوش مصنوعی:
- 🧠 تحلیل محتوای خبرها
- 📈 امتیازدهی کریپتو
- 🎯 انتخاب بهترین خبرها

## 📊 آمار و گزارش

- تعداد خبرهای جمع‌آوری شده
- تعداد خبرهای کریپتو تشخیص داده شده
- تعداد پست‌های ارسال شده
- نرخ موفقیت پست‌ها
- آمار عملکرد سیستم

## ⚠️ نکات مهم

- حداکثر 50 پست در روز (قابل تنظیم)
- فاصله 5 دقیقه بین بررسی‌ها
- رعایت قوانین و مقررات X
- نگهداری امن اطلاعات حساب

## 🆘 عیب‌یابی

اگر مشکلی پیش آمد:
1. لاگ‌ها را در تب "لاگ‌ها" بررسی کنید
2. وضعیت اتصال X را چک کنید
3. تنظیمات را مجدداً بررسی کنید
4. ربات را مجدداً راه‌اندازی کنید

## 📞 پشتیبانی

برای مشکلات فنی، فایل‌های لاگ در پوشه `logs/` را بررسی کنید.
