# 🤖 ربات هوشمند خبرهای کریپتوکارنسی برای X

ربات هوشمند و خودکار برای پست کردن آخرین خبرهای کریپتوکارنسی در شبکه اجتماعی X (تویتر سابق)

## ✨ ویژگی‌های کلیدی

- 🔄 **مانیتورینگ خودکار**: بررسی صفحات خبری هر 5 دقیقه
- 🧠 **تشخیص هوشمند**: شناسایی خبرهای مرتبط با کریپتوکارنسی
- 🚫 **جلوگیری از تکرار**: عدم پست خبرهای تکراری
- 🖥️ **رابط گرافیکی کامل**: کنترل آسان و نمایش وضعیت زنده
- 📊 **آمار زنده**: نمودارهای زمان واقعی و آمار تفصیلی
- 📈 **نمایش فعالیت**: مشاهده تمام فعالیت‌های ربات در زمان واقعی
- 🖥️ **مانیتورینگ سیستم**: نمایش وضعیت CPU، حافظه و اتصالات
- 🔒 **امنیت بالا**: استفاده از تکنیک‌های پیشرفته web scraping

## 🛠️ نصب و راه‌اندازی

### پیش‌نیازها
- Python 3.8 یا بالاتر
- Google Chrome یا Chromium
- اتصال اینترنت پایدار

### مراحل نصب

1. **کلون کردن پروژه**
```bash
git clone <repository-url>
cd crypto-news-bot
```

2. **ایجاد محیط مجازی**
```bash
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate
```

3. **نصب وابستگی‌ها**
```bash
pip install -r requirements.txt
```

4. **تنظیم متغیرهای محیطی**
```bash
cp .env.example .env
# ویرایش فایل .env و وارد کردن اطلاعات X
```

5. **اجرای برنامه**
```bash
# روش اول - اجرای مستقیم
python main.py

# روش دوم - اجرای سریع
python start_bot.py
```

## 📋 نحوه استفاده

1. برنامه را اجرا کنید
2. اطلاعات حساب X خود را وارد کنید
3. لیست صفحات خبری مورد نظر را اضافه کنید
4. تنظیمات فیلتر کریپتو را تنظیم کنید
5. ربات را فعال کنید

## 🏗️ ساختار پروژه

```
crypto_news_bot/
├── crypto_news_bot/
│   ├── config.py           # تنظیمات
│   ├── database/           # مدیریت پایگاه داده
│   ├── scraper/           # Web scraping
│   ├── content_filter/    # فیلتر محتوا
│   ├── poster/            # پست در X
│   ├── gui/               # رابط گرافیکی
│   └── utils/             # ابزارهای کمکی
├── data/                  # فایل‌های داده
├── logs/                  # فایل‌های لاگ
├── config/                # فایل‌های پیکربندی
├── requirements.txt       # وابستگی‌ها
└── main.py               # نقطه ورود
```

## ⚙️ تنظیمات

تمام تنظیمات در فایل `crypto_news_bot/config.py` قابل تغییر هستند:

- فاصله زمانی بررسی خبرها
- کلمات کلیدی کریپتو
- تنظیمات امنیتی
- و بیشتر...

## 🔧 توسعه

برای مشارکت در توسعه:

1. Fork کنید
2. Branch جدید ایجاد کنید
3. تغییرات را commit کنید
4. Pull request ارسال کنید

## ⚠️ هشدارها

- از این ربات مطابق با قوانین X استفاده کنید
- از spam کردن خودداری کنید
- حساب خود را محافظت کنید

## 📄 مجوز

این پروژه تحت مجوز MIT منتشر شده است.

## 🧪 تست کردن

برای اجرای تست‌ها:

```bash
# اجرای همه تست‌ها
python run_tests.py all

# اجرای تست‌های عملکرد
python run_tests.py performance

# اجرای تست خاص
python run_tests.py test_crypto_detector

# بررسی پوشش تست‌ها
python run_tests.py coverage
```

## 🔧 عیب‌یابی

### مشکلات رایج

**1. خطای نصب Chrome Driver:**
```bash
# دانلود دستی ChromeDriver
# از https://chromedriver.chromium.org/
# و قرار دادن در PATH سیستم
```

**2. خطای ورود به X:**
- بررسی صحت اطلاعات در فایل .env
- فعال کردن تایید دو مرحله‌ای
- استفاده از App Password

**3. خطای NLTK:**
```bash
python -c "import nltk; nltk.download('punkt'); nltk.download('stopwords')"
```

**4. خطای حافظه:**
- کاهش تعداد منابع خبری
- افزایش فاصله زمانی بررسی
- بستن برنامه‌های غیرضروری

### لاگ‌ها

لاگ‌های برنامه در پوشه `logs/` ذخیره می‌شوند:
- `main.log`: لاگ‌های کلی
- `scraper.log`: لاگ‌های استخراج
- `poster.log`: لاگ‌های پست کردن
- `gui.log`: لاگ‌های رابط کاربری

## 📚 مستندات تفصیلی

### ساختار پروژه

```
crypto_news_bot/
├── crypto_news_bot/           # کد اصلی
│   ├── config.py             # تنظیمات
│   ├── core/                 # هسته ربات
│   │   └── bot_manager.py    # مدیریت اصلی
│   ├── database/             # پایگاه داده
│   │   └── models.py         # مدل‌های داده
│   ├── scraper/              # استخراج خبر
│   │   └── x_scraper.py      # استخراج از X
│   ├── content_filter/       # فیلتر محتوا
│   │   ├── crypto_detector.py # تشخیص کریپتو
│   │   └── duplicate_filter.py # فیلتر تکرار
│   ├── poster/               # پست کردن
│   │   └── x_poster.py       # پست در X
│   ├── gui/                  # رابط گرافیکی
│   │   ├── main_window.py    # پنجره اصلی
│   │   ├── settings_window.py # تنظیمات
│   │   └── stats_window.py   # آمار
│   └── utils/                # ابزارهای کمکی
│       ├── logger.py         # سیستم لاگ
│       └── scheduler.py      # زمان‌بندی
├── tests/                    # تست‌ها
├── data/                     # داده‌ها
├── logs/                     # لاگ‌ها
├── config/                   # پیکربندی
├── main.py                   # نقطه ورود
├── setup.py                  # نصب خودکار
├── run_tests.py              # اجرای تست‌ها
└── requirements.txt          # وابستگی‌ها
```

### API داخلی

#### CryptoContentDetector

```python
from crypto_news_bot.content_filter.crypto_detector import CryptoContentDetector

detector = CryptoContentDetector()

# تشخیص محتوای کریپتو
is_crypto, confidence = detector.calculate_crypto_score("Bitcoin price rises")
print(f"Is crypto: {is_crypto}, Confidence: {confidence}")

# تحلیل کیفیت
quality = detector.analyze_content_quality("High quality crypto news")
```

#### DatabaseManager

```python
from crypto_news_bot.database.models import DatabaseManager

db = DatabaseManager()

# اضافه کردن خبر
news_id = db.add_news(
    title="Bitcoin News",
    content="Bitcoin reaches new high",
    url="https://example.com",
    source="test",
    is_crypto=True,
    confidence=0.8
)

# دریافت خبرهای پست نشده
unposted = db.get_unposted_crypto_news()
```

#### BotManager

```python
from crypto_news_bot.core.bot_manager import BotManager

bot = BotManager()

# شروع ربات
bot.start()

# دریافت وضعیت
status = bot.get_status()
print(status)

# توقف ربات
bot.stop()
```

## 🔒 امنیت

### نکات امنیتی

1. **محافظت از اطلاعات حساب:**
   - هرگز اطلاعات حساب را در کد قرار ندهید
   - از فایل .env استفاده کنید
   - فایل .env را در .gitignore قرار دهید

2. **استفاده مسئولانه:**
   - از spam کردن خودداری کنید
   - قوانین X را رعایت کنید
   - فاصله مناسب بین پست‌ها رعایت کنید

3. **مدیریت خطا:**
   - لاگ‌ها را مرتب بررسی کنید
   - در صورت خطاهای مکرر، ربات را متوقف کنید
   - از backup منظم استفاده کنید

### تنظیمات امنیتی

```python
# در config.py
SCRAPING_INTERVAL = 5  # حداقل 5 دقیقه
MAX_POSTS_PER_DAY = 50  # حداکثر 50 پست در روز
DELAY_BETWEEN_REQUESTS = 2  # 2 ثانیه تاخیر
```

## 🚀 بهینه‌سازی عملکرد

### تنظیمات بهینه

1. **تنظیمات زمان‌بندی:**
   - فاصله بررسی: 5-10 دقیقه
   - حداکثر پست روزانه: 30-50
   - فاصله بین پست‌ها: 5-10 دقیقه

2. **تنظیمات کیفیت:**
   - حداقل امتیاز کریپتو: 0.3-0.5
   - آستانه تشخیص تکرار: 0.7-0.9

3. **تنظیمات سیستم:**
   - استفاده از SSD برای پایگاه داده
   - حداقل 4GB RAM
   - اتصال اینترنت پایدار

### مانیتورینگ

```python
# بررسی عملکرد
from crypto_news_bot.utils.scheduler import PerformanceMonitor

monitor = PerformanceMonitor()
monitor.record_metric("response_time", 2.5)
monitor.record_metric("error_rate", 5.0)

# دریافت آمار
recent_metrics = monitor.get_recent_metrics("response_time", 60)
alerts = monitor.get_alerts(60)
```

## 🆘 پشتیبانی

### راه‌های دریافت کمک

1. **مستندات:** ابتدا این فایل README را مطالعه کنید
2. **لاگ‌ها:** فایل‌های لاگ را بررسی کنید
3. **تست‌ها:** تست‌ها را اجرا کنید تا مشکل را شناسایی کنید
4. **Issues:** در صورت بروز مشکل، issue جدید ایجاد کنید

### اطلاعات مورد نیاز برای گزارش مشکل

- نسخه سیستم عامل
- نسخه پایتون
- متن کامل خطا
- فایل‌های لاگ مربوطه
- مراحل بازتولید مشکل

### مشارکت در توسعه

1. Fork کنید
2. Branch جدید ایجاد کنید (`git checkout -b feature/amazing-feature`)
3. تغییرات را commit کنید (`git commit -m 'Add amazing feature'`)
4. Push کنید (`git push origin feature/amazing-feature`)
5. Pull Request ایجاد کنید

## 📄 مجوز

این پروژه تحت مجوز MIT منتشر شده است. برای جزئیات بیشتر فایل LICENSE را مطالعه کنید.
