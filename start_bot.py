#!/usr/bin/env python3
"""
اسکریپت شروع سریع ربات
"""

import sys
import os
from pathlib import Path

# اضافه کردن مسیر پروژه
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_requirements():
    """بررسی نیازمندی‌های اساسی"""
    try:
        import tkinter
        print("✅ tkinter موجود است")
    except ImportError:
        print("❌ tkinter یافت نشد. لطفاً Python با پشتیبانی tkinter نصب کنید")
        return False
    
    try:
        import sqlite3
        print("✅ sqlite3 موجود است")
    except ImportError:
        print("❌ sqlite3 یافت نشد")
        return False
    
    return True

def main():
    """تابع اصلی"""
    print("🤖 ربات هوشمند خبرهای کریپتوکارنسی")
    print("=" * 40)
    
    # بررسی نیازمندی‌ها
    if not check_requirements():
        print("\n❌ برخی نیازمندی‌ها یافت نشدند")
        print("لطفاً ابتدا دستور زیر را اجرا کنید:")
        print("pip install -r requirements.txt")
        return
    
    print("\n🚀 در حال راه‌اندازی...")
    
    try:
        # وارد کردن و اجرای برنامه اصلی
        from main import main as run_main
        run_main()
        
    except ImportError as e:
        print(f"❌ خطا در وارد کردن ماژول‌ها: {e}")
        print("لطفاً مطمئن شوید که همه وابستگی‌ها نصب شده‌اند:")
        print("pip install -r requirements.txt")
        
    except Exception as e:
        print(f"❌ خطا در اجرای برنامه: {e}")

if __name__ == "__main__":
    main()
