@echo off
title ربات خبرهای کریپتو
color 0A

echo.
echo ========================================
echo    ربات هوشمند خبرهای کریپتوکارنسی
echo ========================================
echo.

REM بررسی وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python یافت نشد!
    echo لطفاً Python را از python.org دانلود و نصب کنید
    pause
    exit /b 1
)

echo ✅ Python یافت شد
echo.

REM بررسی وجود virtual environment
if not exist "venv" (
    echo 🔧 ایجاد محیط مجازی...
    python -m venv venv
    if errorlevel 1 (
        echo ❌ خطا در ایجاد محیط مجازی
        pause
        exit /b 1
    )
    echo ✅ محیط مجازی ایجاد شد
)

REM فعال کردن محیط مجازی
echo 🔄 فعال‌سازی محیط مجازی...
call venv\Scripts\activate.bat

REM نصب وابستگی‌ها
if not exist "venv\Scripts\pip.exe" (
    echo ❌ pip یافت نشد
    pause
    exit /b 1
)

echo 📦 بررسی و نصب وابستگی‌ها...
venv\Scripts\pip.exe install -r requirements.txt --quiet

if errorlevel 1 (
    echo ⚠️ برخی وابستگی‌ها نصب نشدند، اما ادامه می‌دهیم...
)

echo ✅ وابستگی‌ها آماده
echo.

REM بررسی فایل .env
if not exist ".env" (
    echo ⚠️ فایل .env یافت نشد
    echo لطفاً اطلاعات حساب X خود را در فایل .env وارد کنید
    echo نمونه در فایل .env.example موجود است
    echo.
)

echo 🚀 شروع ربات...
echo.

REM اجرای برنامه
venv\Scripts\python.exe start_bot.py

echo.
echo برنامه بسته شد
pause
