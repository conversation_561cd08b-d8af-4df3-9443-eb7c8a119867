"""
مدل‌های پایگاه داده
"""

import sqlite3
import hashlib
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from pathlib import Path

from crypto_news_bot.config import DATABASE_PATH
from crypto_news_bot.utils.logger import main_logger


class DatabaseManager:
    """مدیریت پایگاه داده SQLite"""
    
    def __init__(self, db_path: Path = DATABASE_PATH):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """ایجاد جداول پایگاه داده"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # جدول خبرها
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS news (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        title TEXT NOT NULL,
                        content TEXT NOT NULL,
                        url TEXT UNIQUE NOT NULL,
                        source TEXT NOT NULL,
                        content_hash TEXT UNIQUE NOT NULL,
                        is_crypto_related BOOLEAN DEFAULT 0,
                        confidence_score REAL DEFAULT 0.0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # جدول پست‌های ارسال شده
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS posted_tweets (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        news_id INTEGER NOT NULL,
                        tweet_text TEXT NOT NULL,
                        tweet_url TEXT,
                        posted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        status TEXT DEFAULT 'success',
                        error_message TEXT,
                        FOREIGN KEY (news_id) REFERENCES news (id)
                    )
                """)
                
                # جدول منابع خبری
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS news_sources (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name TEXT NOT NULL,
                        url TEXT UNIQUE NOT NULL,
                        is_active BOOLEAN DEFAULT 1,
                        last_checked TIMESTAMP,
                        check_interval INTEGER DEFAULT 300,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # جدول تنظیمات
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS settings (
                        key TEXT PRIMARY KEY,
                        value TEXT NOT NULL,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # جدول آمار
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS statistics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        date DATE NOT NULL,
                        news_scraped INTEGER DEFAULT 0,
                        crypto_news_found INTEGER DEFAULT 0,
                        tweets_posted INTEGER DEFAULT 0,
                        errors_count INTEGER DEFAULT 0,
                        UNIQUE(date)
                    )
                """)
                
                # ایجاد ایندکس‌ها
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_news_created_at ON news(created_at)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_news_crypto ON news(is_crypto_related)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_posted_tweets_date ON posted_tweets(posted_at)")
                
                conn.commit()
                main_logger.info("پایگاه داده با موفقیت ایجاد شد")
                
        except Exception as e:
            main_logger.error(f"خطا در ایجاد پایگاه داده: {e}")
            raise
    
    def add_news(self, title: str, content: str, url: str, source: str, 
                 is_crypto: bool = False, confidence: float = 0.0) -> Optional[int]:
        """اضافه کردن خبر جدید"""
        try:
            # ایجاد hash برای محتوا
            content_hash = hashlib.md5(content.encode('utf-8')).hexdigest()
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO news (title, content, url, source, content_hash, 
                                    is_crypto_related, confidence_score)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (title, content, url, source, content_hash, is_crypto, confidence))
                
                news_id = cursor.lastrowid
                conn.commit()
                return news_id
                
        except sqlite3.IntegrityError:
            # خبر تکراری
            return None
        except Exception as e:
            main_logger.error(f"خطا در اضافه کردن خبر: {e}")
            return None
    
    def get_unposted_crypto_news(self, limit: int = 10) -> List[Dict]:
        """دریافت خبرهای کریپتو که هنوز پست نشده‌اند"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT n.* FROM news n
                    LEFT JOIN posted_tweets pt ON n.id = pt.news_id
                    WHERE n.is_crypto_related = 1 
                    AND pt.id IS NULL
                    ORDER BY n.confidence_score DESC, n.created_at DESC
                    LIMIT ?
                """, (limit,))
                
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            main_logger.error(f"خطا در دریافت خبرهای کریپتو: {e}")
            return []
    
    def add_posted_tweet(self, news_id: int, tweet_text: str, 
                        tweet_url: str = None, status: str = "success", 
                        error_message: str = None) -> bool:
        """ثبت پست ارسال شده"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO posted_tweets (news_id, tweet_text, tweet_url, status, error_message)
                    VALUES (?, ?, ?, ?, ?)
                """, (news_id, tweet_text, tweet_url, status, error_message))
                
                conn.commit()
                return True
                
        except Exception as e:
            main_logger.error(f"خطا در ثبت پست: {e}")
            return False

    def add_news_source(self, name: str, url: str, check_interval: int = 300) -> bool:
        """اضافه کردن منبع خبری جدید"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO news_sources (name, url, check_interval)
                    VALUES (?, ?, ?)
                """, (name, url, check_interval))

                conn.commit()
                return True

        except sqlite3.IntegrityError:
            return False  # منبع تکراری
        except Exception as e:
            main_logger.error(f"خطا در اضافه کردن منبع خبری: {e}")
            return False

    def get_active_news_sources(self) -> List[Dict]:
        """دریافت منابع خبری فعال"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                cursor.execute("""
                    SELECT * FROM news_sources
                    WHERE is_active = 1
                    ORDER BY name
                """)

                return [dict(row) for row in cursor.fetchall()]

        except Exception as e:
            main_logger.error(f"خطا در دریافت منابع خبری: {e}")
            return []

    def update_source_last_checked(self, source_id: int) -> bool:
        """به‌روزرسانی زمان آخرین بررسی منبع"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    UPDATE news_sources
                    SET last_checked = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (source_id,))

                conn.commit()
                return True

        except Exception as e:
            main_logger.error(f"خطا در به‌روزرسانی منبع: {e}")
            return False

    def get_setting(self, key: str, default_value: str = None) -> str:
        """دریافت تنظیمات"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT value FROM settings WHERE key = ?", (key,))

                result = cursor.fetchone()
                return result[0] if result else default_value

        except Exception as e:
            main_logger.error(f"خطا در دریافت تنظیمات: {e}")
            return default_value

    def set_setting(self, key: str, value: str) -> bool:
        """تنظیم مقدار"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT OR REPLACE INTO settings (key, value, updated_at)
                    VALUES (?, ?, CURRENT_TIMESTAMP)
                """, (key, value))

                conn.commit()
                return True

        except Exception as e:
            main_logger.error(f"خطا در تنظیم مقدار: {e}")
            return False

    def get_daily_statistics(self, date: str = None) -> Dict:
        """دریافت آمار روزانه"""
        if not date:
            date = datetime.now().strftime('%Y-%m-%d')

        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                cursor.execute("""
                    SELECT * FROM statistics WHERE date = ?
                """, (date,))

                result = cursor.fetchone()
                if result:
                    return dict(result)
                else:
                    # ایجاد رکورد جدید برای امروز
                    cursor.execute("""
                        INSERT INTO statistics (date) VALUES (?)
                    """, (date,))
                    conn.commit()

                    return {
                        'date': date,
                        'news_scraped': 0,
                        'crypto_news_found': 0,
                        'tweets_posted': 0,
                        'errors_count': 0
                    }

        except Exception as e:
            main_logger.error(f"خطا در دریافت آمار: {e}")
            return {}

    def update_statistics(self, field: str, increment: int = 1, date: str = None) -> bool:
        """به‌روزرسانی آمار"""
        if not date:
            date = datetime.now().strftime('%Y-%m-%d')

        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # اطمینان از وجود رکورد
                cursor.execute("""
                    INSERT OR IGNORE INTO statistics (date) VALUES (?)
                """, (date,))

                # به‌روزرسانی فیلد
                cursor.execute(f"""
                    UPDATE statistics
                    SET {field} = {field} + ?
                    WHERE date = ?
                """, (increment, date))

                conn.commit()
                return True

        except Exception as e:
            main_logger.error(f"خطا در به‌روزرسانی آمار: {e}")
            return False
