"""
سیستم استخراج خبر از X (Twitter)
"""

import time
import random
from typing import List, Dict, Optional
from datetime import datetime, timedelta
import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from bs4 import BeautifulSoup
import requests
from fake_useragent import UserAgent

from crypto_news_bot.config import (
    REQUEST_TIMEOUT, MAX_RETRIES, DELAY_BETWEEN_REQUESTS, USER_AGENTS
)
from crypto_news_bot.utils.logger import scraper_logger
from crypto_news_bot.content_filter.crypto_detector import CryptoContentDetector
from crypto_news_bot.content_filter.duplicate_filter import DuplicateContentFilter


class XScraper:
    """کلاس استخراج خبر از X"""
    
    def __init__(self):
        self.driver = None
        self.crypto_detector = CryptoContentDetector()
        self.duplicate_filter = DuplicateContentFilter()
        self.ua = UserAgent()
        self.session = requests.Session()
        self.last_request_time = 0
        
        # تنظیمات rate limiting
        self.min_delay = 2
        self.max_delay = 5
        self.request_count = 0
        self.max_requests_per_hour = 100
        
    def setup_driver(self) -> bool:
        """راه‌اندازی Chrome driver"""
        try:
            options = uc.ChromeOptions()
            
            # تنظیمات مخفی‌سازی
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            
            # تنظیم User Agent
            options.add_argument(f'--user-agent={random.choice(USER_AGENTS)}')
            
            # تنظیمات عملکرد
            options.add_argument('--disable-images')
            options.add_argument('--disable-javascript')  # برای سرعت بیشتر
            
            # حالت headless (اختیاری)
            # options.add_argument('--headless')
            
            self.driver = uc.Chrome(options=options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            scraper_logger.info("Chrome driver با موفقیت راه‌اندازی شد")
            return True
            
        except Exception as e:
            scraper_logger.error(f"خطا در راه‌اندازی driver: {e}")
            return False
    
    def close_driver(self):
        """بستن driver"""
        if self.driver:
            try:
                self.driver.quit()
                scraper_logger.info("Driver بسته شد")
            except Exception as e:
                scraper_logger.error(f"خطا در بستن driver: {e}")
    
    def respect_rate_limit(self):
        """رعایت محدودیت نرخ درخواست"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        # حداقل تاخیر بین درخواست‌ها
        if time_since_last < self.min_delay:
            sleep_time = self.min_delay - time_since_last
            time.sleep(sleep_time)
        
        # تاخیر تصادفی اضافی
        additional_delay = random.uniform(0, self.max_delay - self.min_delay)
        time.sleep(additional_delay)
        
        self.last_request_time = time.time()
        self.request_count += 1
    
    def extract_tweet_data(self, tweet_element) -> Optional[Dict]:
        """استخراج داده‌های توییت از element"""
        try:
            # متن توییت
            text_element = tweet_element.find_element(By.CSS_SELECTOR, '[data-testid="tweetText"]')
            text = text_element.text if text_element else ""
            
            # نام کاربری
            username_element = tweet_element.find_element(By.CSS_SELECTOR, '[data-testid="User-Name"] a')
            username = username_element.get_attribute('href').split('/')[-1] if username_element else ""
            
            # زمان
            time_element = tweet_element.find_element(By.CSS_SELECTOR, 'time')
            timestamp = time_element.get_attribute('datetime') if time_element else ""
            
            # لینک توییت
            link_element = tweet_element.find_element(By.CSS_SELECTOR, 'a[href*="/status/"]')
            tweet_url = link_element.get_attribute('href') if link_element else ""
            
            # آمار تعامل
            stats = {}
            try:
                reply_element = tweet_element.find_element(By.CSS_SELECTOR, '[data-testid="reply"]')
                stats['replies'] = reply_element.get_attribute('aria-label') or "0"
                
                retweet_element = tweet_element.find_element(By.CSS_SELECTOR, '[data-testid="retweet"]')
                stats['retweets'] = retweet_element.get_attribute('aria-label') or "0"
                
                like_element = tweet_element.find_element(By.CSS_SELECTOR, '[data-testid="like"]')
                stats['likes'] = like_element.get_attribute('aria-label') or "0"
            except:
                pass
            
            return {
                'text': text,
                'username': username,
                'timestamp': timestamp,
                'url': tweet_url,
                'stats': stats,
                'scraped_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            scraper_logger.error(f"خطا در استخراج داده توییت: {e}")
            return None
    
    def scrape_user_timeline(self, username: str, max_tweets: int = 20) -> List[Dict]:
        """استخراج توییت‌های یک کاربر"""
        tweets = []
        
        try:
            if not self.driver:
                if not self.setup_driver():
                    return tweets
            
            # رفتن به صفحه کاربر
            url = f"https://twitter.com/{username}"
            self.respect_rate_limit()
            
            scraper_logger.info(f"در حال استخراج توییت‌های {username}")
            self.driver.get(url)
            
            # انتظار برای بارگذاری صفحه
            wait = WebDriverWait(self.driver, 10)
            wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, '[data-testid="tweet"]')))
            
            # اسکرول و جمع‌آوری توییت‌ها
            last_height = self.driver.execute_script("return document.body.scrollHeight")
            tweets_collected = 0
            
            while tweets_collected < max_tweets:
                # پیدا کردن توییت‌ها
                tweet_elements = self.driver.find_elements(By.CSS_SELECTOR, '[data-testid="tweet"]')
                
                for element in tweet_elements[tweets_collected:]:
                    if tweets_collected >= max_tweets:
                        break
                    
                    tweet_data = self.extract_tweet_data(element)
                    if tweet_data and tweet_data['text']:
                        # بررسی تکراری نبودن
                        is_duplicate, similarity, reason = self.duplicate_filter.is_duplicate(tweet_data['text'])
                        
                        if not is_duplicate:
                            # بررسی مرتبط بودن با کریپتو
                            is_crypto, confidence = self.crypto_detector.calculate_crypto_score(tweet_data['text'])
                            
                            tweet_data['is_crypto'] = is_crypto
                            tweet_data['crypto_confidence'] = confidence
                            tweet_data['source'] = username
                            
                            tweets.append(tweet_data)
                            self.duplicate_filter.add_content(tweet_data['text'])
                            
                            tweets_collected += 1
                            scraper_logger.debug(f"توییت جمع‌آوری شد: {tweet_data['text'][:50]}...")
                
                # اسکرول برای توییت‌های بیشتر
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(2)
                
                # بررسی اینکه آیا توییت جدیدی بارگذاری شده یا نه
                new_height = self.driver.execute_script("return document.body.scrollHeight")
                if new_height == last_height:
                    break
                last_height = new_height
            
            scraper_logger.info(f"جمع‌آوری {len(tweets)} توییت از {username} تکمیل شد")
            
        except TimeoutException:
            scraper_logger.warning(f"Timeout در بارگذاری صفحه {username}")
        except Exception as e:
            scraper_logger.error(f"خطا در استخراج توییت‌های {username}: {e}")
        
        return tweets
    
    def scrape_multiple_sources(self, sources: List[Dict], max_tweets_per_source: int = 10) -> List[Dict]:
        """استخراج از چندین منبع"""
        all_tweets = []
        
        for source in sources:
            try:
                username = source.get('url', '').split('/')[-1]
                if not username:
                    continue
                
                tweets = self.scrape_user_timeline(username, max_tweets_per_source)
                all_tweets.extend(tweets)
                
                # تاخیر بین منابع
                time.sleep(random.uniform(5, 10))
                
            except Exception as e:
                scraper_logger.error(f"خطا در استخراج از منبع {source}: {e}")
                continue
        
        return all_tweets
    
    def get_trending_crypto_topics(self) -> List[str]:
        """دریافت موضوعات ترند کریپتو"""
        trending = []
        
        try:
            if not self.driver:
                if not self.setup_driver():
                    return trending
            
            # رفتن به صفحه explore
            self.driver.get("https://twitter.com/explore")
            self.respect_rate_limit()
            
            wait = WebDriverWait(self.driver, 10)
            trend_elements = wait.until(
                EC.presence_of_all_elements_located((By.CSS_SELECTOR, '[data-testid="trend"]'))
            )
            
            for element in trend_elements[:10]:  # 10 ترند اول
                try:
                    trend_text = element.text
                    if self.crypto_detector.calculate_crypto_score(trend_text)[0]:
                        trending.append(trend_text)
                except:
                    continue
            
            scraper_logger.info(f"پیدا شد {len(trending)} ترند کریپتو")
            
        except Exception as e:
            scraper_logger.error(f"خطا در دریافت ترندها: {e}")
        
        return trending
