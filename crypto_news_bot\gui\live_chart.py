"""
نمودار زنده برای نمایش آمار
"""

import tkinter as tk
from tkinter import ttk
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.animation import FuncAnimation
from datetime import datetime, timedelta
from collections import deque
import threading


class LiveChart:
    """کلاس نمودار زنده"""
    
    def __init__(self, parent_frame, title="آمار زنده", max_points=50):
        self.parent_frame = parent_frame
        self.title = title
        self.max_points = max_points
        
        # داده‌های نمودار
        self.times = deque(maxlen=max_points)
        self.news_data = deque(maxlen=max_points)
        self.crypto_data = deque(maxlen=max_points)
        self.posts_data = deque(maxlen=max_points)
        
        # تنظیمات matplotlib برای فارسی
        plt.rcParams['font.family'] = ['Tahoma', 'DejaVu Sans']
        
        # ایجاد نمودار
        self.fig, self.ax = plt.subplots(figsize=(8, 4))
        self.fig.patch.set_facecolor('white')
        
        # تنظیم نمودار
        self.ax.set_title(self.title, fontsize=14, fontweight='bold')
        self.ax.set_xlabel('زمان')
        self.ax.set_ylabel('تعداد')
        self.ax.grid(True, alpha=0.3)
        
        # خطوط نمودار
        self.line_news, = self.ax.plot([], [], 'b-', label='خبرها', linewidth=2)
        self.line_crypto, = self.ax.plot([], [], 'orange', label='کریپتو', linewidth=2)
        self.line_posts, = self.ax.plot([], [], 'g-', label='پست‌ها', linewidth=2)
        
        self.ax.legend(loc='upper left')
        
        # Canvas برای نمایش در tkinter
        self.canvas = FigureCanvasTkAgg(self.fig, parent_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # شروع انیمیشن
        self.animation = FuncAnimation(
            self.fig, self.update_chart, interval=2000, blit=False
        )
        
        # قفل برای thread safety
        self.data_lock = threading.Lock()
        
        # مقادیر قبلی برای محاسبه تغییرات
        self.prev_news = 0
        self.prev_crypto = 0
        self.prev_posts = 0
    
    def add_data_point(self, news_count, crypto_count, posts_count):
        """اضافه کردن نقطه داده جدید"""
        with self.data_lock:
            current_time = datetime.now()
            
            # محاسبه تغییرات نسبت به قبل
            news_delta = news_count - self.prev_news
            crypto_delta = crypto_count - self.prev_crypto
            posts_delta = posts_count - self.prev_posts
            
            # اضافه کردن داده‌ها
            self.times.append(current_time)
            self.news_data.append(news_delta if news_delta >= 0 else 0)
            self.crypto_data.append(crypto_delta if crypto_delta >= 0 else 0)
            self.posts_data.append(posts_delta if posts_delta >= 0 else 0)
            
            # به‌روزرسانی مقادیر قبلی
            self.prev_news = news_count
            self.prev_crypto = crypto_count
            self.prev_posts = posts_count
    
    def update_chart(self, frame):
        """به‌روزرسانی نمودار"""
        with self.data_lock:
            if len(self.times) < 2:
                return self.line_news, self.line_crypto, self.line_posts
            
            # تبدیل زمان‌ها به فرمت قابل نمایش
            time_labels = [t.strftime("%H:%M") for t in self.times]
            
            # به‌روزرسانی داده‌های خطوط
            x_data = list(range(len(self.times)))
            
            self.line_news.set_data(x_data, list(self.news_data))
            self.line_crypto.set_data(x_data, list(self.crypto_data))
            self.line_posts.set_data(x_data, list(self.posts_data))
            
            # تنظیم محدوده‌ها
            if x_data:
                self.ax.set_xlim(0, max(x_data))
                
                # محاسبه حداکثر مقدار برای محور y
                all_values = list(self.news_data) + list(self.crypto_data) + list(self.posts_data)
                if all_values:
                    max_val = max(all_values)
                    self.ax.set_ylim(0, max(max_val * 1.1, 1))
                
                # تنظیم برچسب‌های محور x
                step = max(1, len(x_data) // 10)
                tick_positions = x_data[::step]
                tick_labels = [time_labels[i] for i in range(0, len(time_labels), step)]
                
                self.ax.set_xticks(tick_positions)
                self.ax.set_xticklabels(tick_labels, rotation=45)
            
            # تنظیم مجدد layout
            self.fig.tight_layout()
        
        return self.line_news, self.line_crypto, self.line_posts
    
    def clear_data(self):
        """پاک کردن داده‌های نمودار"""
        with self.data_lock:
            self.times.clear()
            self.news_data.clear()
            self.crypto_data.clear()
            self.posts_data.clear()
            
            self.prev_news = 0
            self.prev_crypto = 0
            self.prev_posts = 0
    
    def destroy(self):
        """نابودی نمودار"""
        if hasattr(self, 'animation'):
            self.animation.event_source.stop()
        
        if hasattr(self, 'canvas'):
            self.canvas.get_tk_widget().destroy()


class RealTimeStatsWidget:
    """ویجت آمار زمان واقعی"""
    
    def __init__(self, parent_frame):
        self.parent_frame = parent_frame
        self.setup_ui()
        
        # داده‌های آمار
        self.stats_history = deque(maxlen=100)
        
    def setup_ui(self):
        """راه‌اندازی رابط کاربری"""
        # فریم اصلی
        main_frame = ttk.Frame(self.parent_frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # قسمت بالا - آمار فوری
        instant_stats = ttk.LabelFrame(main_frame, text="آمار لحظه‌ای", padding=10)
        instant_stats.pack(fill=tk.X, pady=(0, 5))
        
        stats_grid = ttk.Frame(instant_stats)
        stats_grid.pack(fill=tk.X)
        
        # آمار در دقیقه
        ttk.Label(stats_grid, text="📊 در دقیقه گذشته:", font=("Arial", 10, "bold")).grid(row=0, column=0, columnspan=4, sticky=tk.W, pady=(0, 5))
        
        ttk.Label(stats_grid, text="خبرها:").grid(row=1, column=0, sticky=tk.W, padx=(10, 5))
        self.news_per_minute = ttk.Label(stats_grid, text="0", font=("Arial", 12, "bold"), foreground="blue")
        self.news_per_minute.grid(row=1, column=1, sticky=tk.W)
        
        ttk.Label(stats_grid, text="کریپتو:").grid(row=1, column=2, sticky=tk.W, padx=(20, 5))
        self.crypto_per_minute = ttk.Label(stats_grid, text="0", font=("Arial", 12, "bold"), foreground="orange")
        self.crypto_per_minute.grid(row=1, column=3, sticky=tk.W)
        
        # آمار در ساعت
        ttk.Label(stats_grid, text="📈 در ساعت گذشته:", font=("Arial", 10, "bold")).grid(row=2, column=0, columnspan=4, sticky=tk.W, pady=(10, 5))
        
        ttk.Label(stats_grid, text="خبرها:").grid(row=3, column=0, sticky=tk.W, padx=(10, 5))
        self.news_per_hour = ttk.Label(stats_grid, text="0", font=("Arial", 12, "bold"), foreground="blue")
        self.news_per_hour.grid(row=3, column=1, sticky=tk.W)
        
        ttk.Label(stats_grid, text="پست‌ها:").grid(row=3, column=2, sticky=tk.W, padx=(20, 5))
        self.posts_per_hour = ttk.Label(stats_grid, text="0", font=("Arial", 12, "bold"), foreground="green")
        self.posts_per_hour.grid(row=3, column=3, sticky=tk.W)
        
        # قسمت پایین - نمودار زنده
        chart_frame = ttk.LabelFrame(main_frame, text="نمودار زنده", padding=5)
        chart_frame.pack(fill=tk.BOTH, expand=True)
        
        self.live_chart = LiveChart(chart_frame, "فعالیت در زمان واقعی")
    
    def update_stats(self, news_count, crypto_count, posts_count):
        """به‌روزرسانی آمار"""
        current_time = datetime.now()
        
        # اضافه کردن به تاریخچه
        self.stats_history.append({
            'time': current_time,
            'news': news_count,
            'crypto': crypto_count,
            'posts': posts_count
        })
        
        # محاسبه آمار دقیقه گذشته
        minute_ago = current_time - timedelta(minutes=1)
        minute_stats = [s for s in self.stats_history if s['time'] > minute_ago]
        
        if len(minute_stats) >= 2:
            news_diff = minute_stats[-1]['news'] - minute_stats[0]['news']
            crypto_diff = minute_stats[-1]['crypto'] - minute_stats[0]['crypto']
            
            self.news_per_minute.config(text=str(max(0, news_diff)))
            self.crypto_per_minute.config(text=str(max(0, crypto_diff)))
        
        # محاسبه آمار ساعت گذشته
        hour_ago = current_time - timedelta(hours=1)
        hour_stats = [s for s in self.stats_history if s['time'] > hour_ago]
        
        if len(hour_stats) >= 2:
            news_diff = hour_stats[-1]['news'] - hour_stats[0]['news']
            posts_diff = hour_stats[-1]['posts'] - hour_stats[0]['posts']
            
            self.news_per_hour.config(text=str(max(0, news_diff)))
            self.posts_per_hour.config(text=str(max(0, posts_diff)))
        
        # به‌روزرسانی نمودار
        self.live_chart.add_data_point(news_count, crypto_count, posts_count)
    
    def clear_stats(self):
        """پاک کردن آمار"""
        self.stats_history.clear()
        self.live_chart.clear_data()
        
        # بازنشانی برچسب‌ها
        self.news_per_minute.config(text="0")
        self.crypto_per_minute.config(text="0")
        self.news_per_hour.config(text="0")
        self.posts_per_hour.config(text="0")
    
    def destroy(self):
        """نابودی ویجت"""
        if hasattr(self, 'live_chart'):
            self.live_chart.destroy()
