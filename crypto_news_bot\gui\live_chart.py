"""
نمودار زنده برای نمایش آمار
"""

import tkinter as tk
from tkinter import ttk
from datetime import datetime, timedelta
from collections import deque
import threading

# حذف matplotlib برای سادگی
try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    from matplotlib.animation import FuncAnimation
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False


class LiveChart:
    """کلاس نمودار زنده ساده"""

    def __init__(self, parent_frame, title="آمار زنده", max_points=50):
        self.parent_frame = parent_frame
        self.title = title
        self.max_points = max_points

        # داده‌های نمودار
        self.times = deque(maxlen=max_points)
        self.news_data = deque(maxlen=max_points)
        self.crypto_data = deque(maxlen=max_points)
        self.posts_data = deque(maxlen=max_points)

        # قفل برای thread safety
        self.data_lock = threading.Lock()

        # مقادیر قبلی برای محاسبه تغییرات
        self.prev_news = 0
        self.prev_crypto = 0
        self.prev_posts = 0

        # ایجاد نمودار ساده با tkinter
        self.create_simple_chart()

    def create_simple_chart(self):
        """ایجاد نمودار ساده با tkinter"""
        # فریم اصلی
        chart_frame = ttk.LabelFrame(self.parent_frame, text=self.title, padding=10)
        chart_frame.pack(fill=tk.BOTH, expand=True)

        # نمایش آمار به صورت متنی
        stats_frame = ttk.Frame(chart_frame)
        stats_frame.pack(fill=tk.X, pady=5)

        # خبرها
        ttk.Label(stats_frame, text="📰 خبرها:", font=("Arial", 12, "bold")).grid(row=0, column=0, sticky=tk.W, padx=5)
        self.news_display = ttk.Label(stats_frame, text="0", font=("Arial", 14, "bold"), foreground="blue")
        self.news_display.grid(row=0, column=1, sticky=tk.W, padx=10)

        # کریپتو
        ttk.Label(stats_frame, text="₿ کریپتو:", font=("Arial", 12, "bold")).grid(row=0, column=2, sticky=tk.W, padx=20)
        self.crypto_display = ttk.Label(stats_frame, text="0", font=("Arial", 14, "bold"), foreground="orange")
        self.crypto_display.grid(row=0, column=3, sticky=tk.W, padx=10)

        # پست‌ها
        ttk.Label(stats_frame, text="📤 پست‌ها:", font=("Arial", 12, "bold")).grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.posts_display = ttk.Label(stats_frame, text="0", font=("Arial", 14, "bold"), foreground="green")
        self.posts_display.grid(row=1, column=1, sticky=tk.W, padx=10, pady=5)

        # آخرین به‌روزرسانی
        ttk.Label(stats_frame, text="🕒 آخرین:", font=("Arial", 12, "bold")).grid(row=1, column=2, sticky=tk.W, padx=20, pady=5)
        self.last_update = ttk.Label(stats_frame, text="--:--", font=("Arial", 12))
        self.last_update.grid(row=1, column=3, sticky=tk.W, padx=10, pady=5)

        # نوار پیشرفت ساده
        progress_frame = ttk.Frame(chart_frame)
        progress_frame.pack(fill=tk.X, pady=10)

        ttk.Label(progress_frame, text="فعالیت:", font=("Arial", 10)).pack(side=tk.LEFT)
        self.activity_bar = ttk.Progressbar(progress_frame, mode='indeterminate', length=200)
        self.activity_bar.pack(side=tk.LEFT, padx=10)

        # تاریخچه ساده
        history_frame = ttk.LabelFrame(chart_frame, text="تاریخچه اخیر", padding=5)
        history_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        self.history_text = tk.Text(history_frame, height=8, wrap=tk.WORD, font=("Consolas", 9))
        history_scroll = ttk.Scrollbar(history_frame, orient=tk.VERTICAL, command=self.history_text.yview)
        self.history_text.configure(yscrollcommand=history_scroll.set)

        self.history_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        history_scroll.pack(side=tk.RIGHT, fill=tk.Y)
    
    def add_data_point(self, news_count, crypto_count, posts_count):
        """اضافه کردن نقطه داده جدید"""
        with self.data_lock:
            current_time = datetime.now()

            # محاسبه تغییرات نسبت به قبل
            news_delta = news_count - self.prev_news
            crypto_delta = crypto_count - self.prev_crypto
            posts_delta = posts_count - self.prev_posts

            # اضافه کردن داده‌ها
            self.times.append(current_time)
            self.news_data.append(news_delta if news_delta >= 0 else 0)
            self.crypto_data.append(crypto_delta if crypto_delta >= 0 else 0)
            self.posts_data.append(posts_delta if posts_delta >= 0 else 0)

            # به‌روزرسانی نمایش
            self.update_display(news_count, crypto_count, posts_count)

            # اضافه کردن به تاریخچه
            if news_delta > 0 or crypto_delta > 0 or posts_delta > 0:
                time_str = current_time.strftime("%H:%M:%S")
                history_line = f"[{time_str}] خبر:{news_delta} کریپتو:{crypto_delta} پست:{posts_delta}\n"
                self.history_text.insert(tk.END, history_line)
                self.history_text.see(tk.END)

                # محدود کردن تاریخچه
                lines = self.history_text.get(1.0, tk.END).split('\n')
                if len(lines) > 20:
                    self.history_text.delete(1.0, "2.0")

            # به‌روزرسانی مقادیر قبلی
            self.prev_news = news_count
            self.prev_crypto = crypto_count
            self.prev_posts = posts_count

    def update_display(self, news_count, crypto_count, posts_count):
        """به‌روزرسانی نمایش"""
        self.news_display.config(text=str(news_count))
        self.crypto_display.config(text=str(crypto_count))
        self.posts_display.config(text=str(posts_count))

        # به‌روزرسانی زمان
        current_time = datetime.now().strftime("%H:%M:%S")
        self.last_update.config(text=current_time)

        # انیمیشن نوار پیشرفت
        if news_count > self.prev_news or crypto_count > self.prev_crypto:
            self.activity_bar.start(10)
            # توقف بعد از 2 ثانیه
            self.parent_frame.after(2000, self.activity_bar.stop)
    
    def update_chart(self, frame=None):
        """به‌روزرسانی نمودار - حذف شده برای سادگی"""
        pass
    
    def clear_data(self):
        """پاک کردن داده‌های نمودار"""
        with self.data_lock:
            self.times.clear()
            self.news_data.clear()
            self.crypto_data.clear()
            self.posts_data.clear()

            self.prev_news = 0
            self.prev_crypto = 0
            self.prev_posts = 0

            # پاک کردن نمایش
            self.news_display.config(text="0")
            self.crypto_display.config(text="0")
            self.posts_display.config(text="0")
            self.last_update.config(text="--:--")
            self.history_text.delete(1.0, tk.END)

    def destroy(self):
        """نابودی نمودار"""
        # حذف matplotlib dependencies
        pass


class RealTimeStatsWidget:
    """ویجت آمار زمان واقعی"""
    
    def __init__(self, parent_frame):
        self.parent_frame = parent_frame
        self.setup_ui()
        
        # داده‌های آمار
        self.stats_history = deque(maxlen=100)
        
    def setup_ui(self):
        """راه‌اندازی رابط کاربری"""
        # فریم اصلی
        main_frame = ttk.Frame(self.parent_frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # قسمت بالا - آمار فوری
        instant_stats = ttk.LabelFrame(main_frame, text="آمار لحظه‌ای", padding=10)
        instant_stats.pack(fill=tk.X, pady=(0, 5))
        
        stats_grid = ttk.Frame(instant_stats)
        stats_grid.pack(fill=tk.X)
        
        # آمار در دقیقه
        ttk.Label(stats_grid, text="📊 در دقیقه گذشته:", font=("Arial", 10, "bold")).grid(row=0, column=0, columnspan=4, sticky=tk.W, pady=(0, 5))
        
        ttk.Label(stats_grid, text="خبرها:").grid(row=1, column=0, sticky=tk.W, padx=(10, 5))
        self.news_per_minute = ttk.Label(stats_grid, text="0", font=("Arial", 12, "bold"), foreground="blue")
        self.news_per_minute.grid(row=1, column=1, sticky=tk.W)
        
        ttk.Label(stats_grid, text="کریپتو:").grid(row=1, column=2, sticky=tk.W, padx=(20, 5))
        self.crypto_per_minute = ttk.Label(stats_grid, text="0", font=("Arial", 12, "bold"), foreground="orange")
        self.crypto_per_minute.grid(row=1, column=3, sticky=tk.W)
        
        # آمار در ساعت
        ttk.Label(stats_grid, text="📈 در ساعت گذشته:", font=("Arial", 10, "bold")).grid(row=2, column=0, columnspan=4, sticky=tk.W, pady=(10, 5))
        
        ttk.Label(stats_grid, text="خبرها:").grid(row=3, column=0, sticky=tk.W, padx=(10, 5))
        self.news_per_hour = ttk.Label(stats_grid, text="0", font=("Arial", 12, "bold"), foreground="blue")
        self.news_per_hour.grid(row=3, column=1, sticky=tk.W)
        
        ttk.Label(stats_grid, text="پست‌ها:").grid(row=3, column=2, sticky=tk.W, padx=(20, 5))
        self.posts_per_hour = ttk.Label(stats_grid, text="0", font=("Arial", 12, "bold"), foreground="green")
        self.posts_per_hour.grid(row=3, column=3, sticky=tk.W)
        
        # قسمت پایین - نمودار زنده
        chart_frame = ttk.LabelFrame(main_frame, text="نمودار زنده", padding=5)
        chart_frame.pack(fill=tk.BOTH, expand=True)
        
        self.live_chart = LiveChart(chart_frame, "فعالیت در زمان واقعی")
    
    def update_stats(self, news_count, crypto_count, posts_count):
        """به‌روزرسانی آمار"""
        current_time = datetime.now()
        
        # اضافه کردن به تاریخچه
        self.stats_history.append({
            'time': current_time,
            'news': news_count,
            'crypto': crypto_count,
            'posts': posts_count
        })
        
        # محاسبه آمار دقیقه گذشته
        minute_ago = current_time - timedelta(minutes=1)
        minute_stats = [s for s in self.stats_history if s['time'] > minute_ago]
        
        if len(minute_stats) >= 2:
            news_diff = minute_stats[-1]['news'] - minute_stats[0]['news']
            crypto_diff = minute_stats[-1]['crypto'] - minute_stats[0]['crypto']
            
            self.news_per_minute.config(text=str(max(0, news_diff)))
            self.crypto_per_minute.config(text=str(max(0, crypto_diff)))
        
        # محاسبه آمار ساعت گذشته
        hour_ago = current_time - timedelta(hours=1)
        hour_stats = [s for s in self.stats_history if s['time'] > hour_ago]
        
        if len(hour_stats) >= 2:
            news_diff = hour_stats[-1]['news'] - hour_stats[0]['news']
            posts_diff = hour_stats[-1]['posts'] - hour_stats[0]['posts']
            
            self.news_per_hour.config(text=str(max(0, news_diff)))
            self.posts_per_hour.config(text=str(max(0, posts_diff)))
        
        # به‌روزرسانی نمودار
        self.live_chart.add_data_point(news_count, crypto_count, posts_count)
    
    def clear_stats(self):
        """پاک کردن آمار"""
        self.stats_history.clear()
        self.live_chart.clear_data()
        
        # بازنشانی برچسب‌ها
        self.news_per_minute.config(text="0")
        self.crypto_per_minute.config(text="0")
        self.news_per_hour.config(text="0")
        self.posts_per_hour.config(text="0")
    
    def destroy(self):
        """نابودی ویجت"""
        if hasattr(self, 'live_chart'):
            self.live_chart.destroy()
